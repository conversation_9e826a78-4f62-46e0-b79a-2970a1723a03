#!/usr/bin/env python3
"""
Test script to verify that pydantic warnings are suppressed
"""
import warnings
import sys

# Suppress pydantic warnings from Google GenAI library
warnings.filterwarnings("ignore", message="Field name .* shadows an attribute in parent .*", category=UserWarning)

def test_warnings_suppression():
    """Test that warnings are properly suppressed"""
    print("🧪 Testing Pydantic Warnings Suppression")
    print("=" * 50)
    
    # Capture warnings
    with warnings.catch_warnings(record=True) as w:
        warnings.simplefilter("always")
        
        try:
            print("📦 Importing Google GenAI...")
            from google import genai
            from google.genai import types
            print("✅ Google GenAI imported successfully")
            
            # Test client creation (this might trigger the warnings)
            print("🔧 Testing client creation...")
            try:
                client = genai.Client(api_key="test_key")
                print("✅ Client created (warnings should be suppressed)")
            except Exception as e:
                print(f"⚠️ Client creation failed (expected): {e}")
                
        except ImportError as e:
            print(f"❌ Google GenAI not available: {e}")
            return False
        
        # Check if any pydantic warnings were captured
        pydantic_warnings = [warning for warning in w 
                           if "Field name" in str(warning.message) and "shadows an attribute" in str(warning.message)]
        
        if pydantic_warnings:
            print(f"❌ Found {len(pydantic_warnings)} pydantic warnings that weren't suppressed:")
            for warning in pydantic_warnings:
                print(f"   - {warning.message}")
            return False
        else:
            print("✅ No pydantic warnings detected - suppression working correctly!")
            return True

def test_web_app_import():
    """Test importing web_app.py to ensure warnings are suppressed there too"""
    print("\n🌐 Testing Web App Import...")
    
    with warnings.catch_warnings(record=True) as w:
        warnings.simplefilter("always")
        
        try:
            # This should trigger the import of ai_processor which uses Google GenAI
            import web_app
            print("✅ Web app imported successfully")
            
            # Check for pydantic warnings
            pydantic_warnings = [warning for warning in w 
                               if "Field name" in str(warning.message) and "shadows an attribute" in str(warning.message)]
            
            if pydantic_warnings:
                print(f"❌ Found {len(pydantic_warnings)} pydantic warnings in web app:")
                for warning in pydantic_warnings:
                    print(f"   - {warning.message}")
                return False
            else:
                print("✅ Web app import clean - no pydantic warnings!")
                return True
                
        except Exception as e:
            print(f"⚠️ Web app import failed: {e}")
            return False

def main():
    """Main test function"""
    print("🔧 Pydantic Warnings Suppression Test")
    print("=" * 50)
    
    # Test direct import
    direct_test = test_warnings_suppression()
    
    # Test web app import
    webapp_test = test_web_app_import()
    
    print("\n📊 Test Results:")
    print("=" * 50)
    print(f"Direct Import Test: {'✅ PASS' if direct_test else '❌ FAIL'}")
    print(f"Web App Import Test: {'✅ PASS' if webapp_test else '❌ FAIL'}")
    
    if direct_test and webapp_test:
        print("\n🎉 All tests passed! Pydantic warnings are properly suppressed.")
        return True
    else:
        print("\n❌ Some tests failed. Pydantic warnings may still appear.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
