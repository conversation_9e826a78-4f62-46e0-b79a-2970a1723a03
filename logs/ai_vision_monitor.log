[2025-09-05 21:32:41] INFO     📹 Initializing real RTSP camera
[2025-09-05 21:32:41] INFO     Gemini AI client initialized successfully
[2025-09-05 21:32:41] INFO     🤖 Testing AI processor connection...
[2025-09-05 21:32:43] ERROR    Gemini API connection test failed: 400 API key expired. Please renew the API key. [reason: "API_KEY_INVALID"
domain: "googleapis.com"
metadata {
  key: "service"
  value: "generativelanguage.googleapis.com"
}
, locale: "en-US"
message: "API key expired. Please renew the API key."
]
[2025-09-05 21:32:43] WARNING  AI processor test failed. License plate detection may not work.
[2025-09-05 21:32:43] INFO     📷 Testing camera connection...
[2025-09-05 21:32:43] INFO     🔄 Attempting to connect to real RTSP camera...
[2025-09-05 21:32:43] ERROR    RTSP URL not configured
[2025-09-05 21:32:43] WARNING  ❌ Real camera connection failed after all attempts
[2025-09-05 21:32:43] INFO     🎭 Switching to mock camera for demonstration
[2025-09-05 21:32:43] INFO     Connecting to mock camera...
[2025-09-05 21:32:44] INFO     Mock camera connected successfully
[2025-09-05 21:32:44] INFO     ⏰ Scheduling capture every 1 minute(s)
[2025-09-05 21:32:58] INFO     Received signal 2, shutting down gracefully...
[2025-09-05 21:32:58] INFO     🛑 Stopping License Plate Monitoring System...
[2025-09-05 21:32:58] INFO     Disconnecting from mock camera
[2025-09-05 21:32:58] INFO     ✅ License Plate Monitoring System stopped
[2025-09-05 21:32:59] INFO     🛑 Stopping License Plate Monitoring System...
[2025-09-05 21:32:59] INFO     ✅ License Plate Monitoring System stopped
[2025-09-05 21:41:56] INFO     ✅ Configuration loaded from config.json
[2025-09-05 21:41:57] INFO     🔄 Mode changed to: object_detection
[2025-09-05 21:41:57] INFO     🎯 Operating in Object Detection mode
[2025-09-05 21:41:57] INFO     🎭 Using mock camera for testing
[2025-09-05 21:41:57] INFO     Gemini AI client initialized successfully
[2025-09-05 21:41:57] INFO     Connecting to mock camera...
[2025-09-05 21:41:58] INFO     Mock camera connected successfully
[2025-09-05 21:42:23] INFO     ✅ Configuration loaded from config.json
[2025-09-05 21:42:23] INFO     🔄 Mode changed to: object_detection
[2025-09-05 21:42:23] INFO     🎯 Operating in Object Detection mode
[2025-09-05 21:42:23] INFO     🎭 Using mock camera for testing
[2025-09-05 21:42:23] INFO     Gemini AI client initialized successfully
[2025-09-05 21:42:23] INFO     Connecting to mock camera...
[2025-09-05 21:42:24] INFO     Mock camera connected successfully
[2025-09-05 21:42:24] INFO     Generated mock image without license plate
[2025-09-05 21:42:24] INFO     Mock frame captured and saved: captured_images/mock_capture_20250905_214224.jpg
[2025-09-05 21:42:24] INFO     Processing image for Object Detection: captured_images/mock_capture_20250905_214224.jpg
[2025-09-05 21:42:26] INFO     API call successful: {'success': True, 'mode': 'object_detection', 'description': 'Empty road with white dashed lines under a light blue sky.', 'word_count': 11, 'raw_response': 'Empty road with white dashed lines under a light blue sky.', 'image_path': 'captured_images/mock_capture_20250905_214224.jpg'}
[2025-09-05 21:42:33] INFO     ✅ Configuration loaded from config.json
[2025-09-05 21:42:34] INFO     🔄 Mode changed to: license_plate
[2025-09-05 21:42:34] INFO     🎯 Operating in License Plate Detection mode
[2025-09-05 21:42:34] INFO     🎭 Using mock camera for testing
[2025-09-05 21:42:34] INFO     Gemini AI client initialized successfully
[2025-09-05 21:42:34] INFO     Connecting to mock camera...
[2025-09-05 21:42:35] INFO     Mock camera connected successfully
[2025-09-05 21:42:35] INFO     Generated mock image with license plate: MNO678
[2025-09-05 21:42:35] INFO     Mock frame captured and saved: captured_images/mock_capture_20250905_214235.jpg
[2025-09-05 21:42:35] INFO     Processing image for License Plate Detection: captured_images/mock_capture_20250905_214235.jpg
[2025-09-05 21:42:37] INFO     API call successful: {'success': True, 'license_plates': [{'plate_number': 'MNO678', 'location': 'On the rear of a blue rectangular object (simulating a vehicle) on the road.', 'confidence': 'High', 'details': 'The license plate is white text on a dark blue background within a blue rectangular frame.'}], 'plates_detected': 1, 'raw_response': 'LICENSE PLATES DETECTED: 1\n\nPlate 1:\n- Number: MNO678\n- Location: On the rear of a blue rectangular object (simulating a vehicle) on the road.\n- Confidence: High\n- Details: The license plate is white text on a dark blue background within a blue rectangular frame.', 'image_path': 'captured_images/mock_capture_20250905_214235.jpg'}
[2025-09-05 21:42:37] INFO     License plates detected in captured_images/mock_capture_20250905_214235.jpg: [{'plate_number': 'MNO678', 'location': 'On the rear of a blue rectangular object (simulating a vehicle) on the road.', 'confidence': 'High', 'details': 'The license plate is white text on a dark blue background within a blue rectangular frame.'}]
[2025-09-05 21:43:28] INFO     ✅ Configuration loaded from config.json
[2025-09-05 21:43:29] INFO     🔄 Mode changed to: object_detection
[2025-09-05 21:43:29] INFO     🎯 Operating in Object Detection mode
[2025-09-05 21:43:29] INFO     📹 Initializing real RTSP camera
[2025-09-05 21:43:29] INFO     Gemini AI client initialized successfully
[2025-09-05 21:43:29] INFO     🔌 Connecting to RTSP camera: rtsp://***:***@***:***@*************:1935
[2025-09-05 21:43:29] WARNING  ⚠️ Failed to open RTSP stream (attempt 1)
[2025-09-05 21:43:29] INFO     🔄 Retry 2/3 in 2 seconds...
[2025-09-05 21:43:31] INFO     🔌 Connecting to RTSP camera: rtsp://***:***@***:***@*************:1935
[2025-09-05 21:43:31] WARNING  ⚠️ Failed to open RTSP stream (attempt 2)
[2025-09-05 21:43:31] INFO     🔄 Retry 3/3 in 4 seconds...
[2025-09-05 21:43:35] INFO     🔌 Connecting to RTSP camera: rtsp://***:***@***:***@*************:1935
[2025-09-05 21:43:35] WARNING  ⚠️ Failed to open RTSP stream (attempt 3)
[2025-09-05 21:43:35] ERROR    ❌ Failed to connect to RTSP camera after 3 attempts
[2025-09-05 21:44:09] INFO     ✅ Configuration loaded from config.json
[2025-09-05 21:44:09] INFO     🔄 Mode changed to: object_detection
[2025-09-05 21:44:09] INFO     🎯 Operating in Object Detection mode
[2025-09-05 21:44:09] INFO     📹 Initializing real RTSP camera
[2025-09-05 21:44:09] INFO     Gemini AI client initialized successfully
[2025-09-05 21:44:09] INFO     🔌 Connecting to RTSP camera: rtsp://***:***@*************:1935
[2025-09-05 21:44:13] INFO     ✅ Successfully connected to RTSP camera (retry 1, 2 successful reads)
[2025-09-05 21:44:13] INFO     📐 Frame size: (480, 640, 3)
[2025-09-05 21:44:13] INFO     ✅ Image captured and saved: captured_images/capture_20250905_214413.jpg
[2025-09-05 21:44:13] INFO     Image captured successfully: captured_images/capture_20250905_214413.jpg
[2025-09-05 21:44:13] INFO     Processing image for Object Detection: captured_images/capture_20250905_214413.jpg
[2025-09-05 21:44:14] INFO     API call successful: {'success': True, 'mode': 'object_detection', 'description': 'Person looking down near large illuminated number three sculpture.', 'word_count': 9, 'raw_response': 'Person looking down near large illuminated number three sculpture.', 'image_path': 'captured_images/capture_20250905_214413.jpg'}
[2025-09-05 21:44:28] INFO     ✅ Configuration loaded from config.json
[2025-09-05 21:44:29] INFO     🔄 Mode changed to: object_detection
[2025-09-05 21:44:29] INFO     🎯 Operating in Object Detection mode
[2025-09-05 21:44:29] INFO     📹 Initializing real RTSP camera
[2025-09-05 21:44:29] INFO     Gemini AI client initialized successfully
[2025-09-05 21:44:29] INFO     🤖 Testing AI processor connection...
[2025-09-05 21:44:30] INFO     Gemini API connection test successful. Response: API connection successful...
[2025-09-05 21:44:30] INFO     📷 Testing camera connection...
[2025-09-05 21:44:30] INFO     🔄 Attempting to connect to real RTSP camera...
[2025-09-05 21:44:30] INFO     🔌 Connecting to RTSP camera: rtsp://***:***@*************:1935
[2025-09-05 21:44:33] INFO     ✅ Successfully connected to RTSP camera (retry 1, 2 successful reads)
[2025-09-05 21:44:33] INFO     📐 Frame size: (480, 640, 3)
[2025-09-05 21:44:33] INFO     ✅ Real RTSP camera connected successfully!
[2025-09-05 21:44:33] INFO     ⏰ Scheduling capture every 1 minute(s)
[2025-09-05 21:44:38] INFO     Received signal 15, shutting down gracefully...
[2025-09-05 21:44:38] INFO     🛑 Stopping License Plate Monitoring System...
[2025-09-05 21:44:38] INFO     🔌 Disconnected from RTSP camera
[2025-09-05 21:44:38] INFO     ✅ License Plate Monitoring System stopped
[2025-09-05 21:44:39] INFO     🛑 Stopping License Plate Monitoring System...
[2025-09-05 21:44:39] INFO     🔌 Disconnected from RTSP camera
[2025-09-05 21:44:39] INFO     ✅ License Plate Monitoring System stopped
[2025-09-05 21:45:40] INFO     ✅ Configuration loaded from config.json
[2025-09-05 21:45:40] INFO     🔄 Mode changed to: object_detection
[2025-09-05 21:45:40] INFO     🎯 Operating in Object Detection mode
[2025-09-05 21:45:40] INFO     📹 Initializing real RTSP camera
[2025-09-05 21:45:40] INFO     Gemini AI client initialized successfully
[2025-09-05 21:45:40] INFO     🔌 Connecting to RTSP camera: rtsp://***:***@*************:1935
[2025-09-05 21:45:43] INFO     ✅ Successfully connected to RTSP camera (retry 1, 2 successful reads)
[2025-09-05 21:45:43] INFO     📐 Frame size: (480, 640, 3)
[2025-09-05 21:45:43] INFO     ✅ Image captured and saved: captured_images/capture_20250905_214543.jpg
[2025-09-05 21:45:43] INFO     Image captured successfully: captured_images/capture_20250905_214543.jpg
[2025-09-05 21:45:43] INFO     Processing image for Object Detection: captured_images/capture_20250905_214543.jpg
[2025-09-05 21:45:43] ERROR    Error processing image captured_images/capture_20250905_214543.jpg: 400 API key expired. Please renew the API key. [reason: "API_KEY_INVALID"
domain: "googleapis.com"
metadata {
  key: "service"
  value: "generativelanguage.googleapis.com"
}
, locale: "en-US"
message: "API key expired. Please renew the API key."
]
[2025-09-05 21:45:43] ERROR    API call failed: Error processing image captured_images/capture_20250905_214543.jpg: 400 API key expired. Please renew the API key. [reason: "API_KEY_INVALID"
domain: "googleapis.com"
metadata {
  key: "service"
  value: "generativelanguage.googleapis.com"
}
, locale: "en-US"
message: "API key expired. Please renew the API key."
]
[2025-09-05 21:47:35] INFO     ✅ Configuration loaded from config.json
[2025-09-05 21:47:36] INFO     🔄 Mode changed to: object_detection
[2025-09-05 21:47:36] INFO     🎯 Operating in Object Detection mode
[2025-09-05 21:47:36] INFO     📹 Initializing real RTSP camera
[2025-09-05 21:47:36] INFO     Gemini AI client initialized successfully
[2025-09-05 21:47:36] INFO     🔌 Connecting to RTSP camera: rtsp://***:***@*************:1935
[2025-09-05 21:47:39] INFO     ✅ Successfully connected to RTSP camera (retry 1, 2 successful reads)
[2025-09-05 21:47:39] INFO     📐 Frame size: (480, 640, 3)
[2025-09-05 21:47:39] INFO     ✅ Image captured and saved: captured_images/capture_20250905_214739.jpg
[2025-09-05 21:47:39] INFO     Image captured successfully: captured_images/capture_20250905_214739.jpg
[2025-09-05 21:47:39] INFO     Processing image for Object Detection: captured_images/capture_20250905_214739.jpg
[2025-09-05 21:47:41] ERROR    Error processing image captured_images/capture_20250905_214739.jpg: 400 API key expired. Please renew the API key. [reason: "API_KEY_INVALID"
domain: "googleapis.com"
metadata {
  key: "service"
  value: "generativelanguage.googleapis.com"
}
, locale: "en-US"
message: "API key expired. Please renew the API key."
]
[2025-09-05 21:47:41] ERROR    API call failed: Error processing image captured_images/capture_20250905_214739.jpg: 400 API key expired. Please renew the API key. [reason: "API_KEY_INVALID"
domain: "googleapis.com"
metadata {
  key: "service"
  value: "generativelanguage.googleapis.com"
}
, locale: "en-US"
message: "API key expired. Please renew the API key."
]
[2025-09-05 21:55:23] INFO     ✅ Configuration loaded from config.json
[2025-09-05 21:55:24] INFO     🔄 Mode changed to: object_detection
[2025-09-05 21:55:24] INFO     🎯 Operating in Object Detection mode
[2025-09-05 21:55:24] INFO     📹 Initializing real RTSP camera
[2025-09-05 21:55:25] INFO     Gemini AI client initialized successfully
[2025-09-05 21:55:25] INFO     🔌 Connecting to RTSP camera: rtsp://***:***@*************:1935
[2025-09-05 21:55:28] INFO     ✅ Successfully connected to RTSP camera (retry 1, 2 successful reads)
[2025-09-05 21:55:28] INFO     📐 Frame size: (480, 640, 3)
[2025-09-05 21:55:28] INFO     ✅ Image captured and saved: captured_images/capture_20250905_215528.jpg
[2025-09-05 21:55:28] INFO     Image captured successfully: captured_images/capture_20250905_215528.jpg
[2025-09-05 21:55:28] INFO     Processing image for Object Detection: captured_images/capture_20250905_215528.jpg
[2025-09-05 21:55:28] ERROR    Error processing image captured_images/capture_20250905_215528.jpg: Part.from_text() takes 1 positional argument but 2 were given
[2025-09-05 21:55:28] ERROR    API call failed: Error processing image captured_images/capture_20250905_215528.jpg: Part.from_text() takes 1 positional argument but 2 were given
[2025-09-05 21:55:47] INFO     ✅ Configuration loaded from config.json
[2025-09-05 21:55:48] INFO     🔄 Mode changed to: object_detection
[2025-09-05 21:55:48] INFO     🎯 Operating in Object Detection mode
[2025-09-05 21:55:48] INFO     📹 Initializing real RTSP camera
[2025-09-05 21:55:48] INFO     Gemini AI client initialized successfully
[2025-09-05 21:55:48] INFO     🔌 Connecting to RTSP camera: rtsp://***:***@*************:1935
[2025-09-05 21:55:48] WARNING  ⚠️ Failed to open RTSP stream (attempt 1)
[2025-09-05 21:55:48] INFO     🔄 Retry 2/3 in 2 seconds...
[2025-09-05 21:55:50] INFO     🔌 Connecting to RTSP camera: rtsp://***:***@*************:1935
[2025-09-05 21:55:50] WARNING  ⚠️ Failed to open RTSP stream (attempt 2)
[2025-09-05 21:55:50] INFO     🔄 Retry 3/3 in 4 seconds...
[2025-09-05 21:55:54] INFO     🔌 Connecting to RTSP camera: rtsp://***:***@*************:1935
[2025-09-05 21:55:55] WARNING  ⚠️ Failed to open RTSP stream (attempt 3)
[2025-09-05 21:55:55] ERROR    ❌ Failed to connect to RTSP camera after 3 attempts
[2025-09-05 21:56:02] INFO     ✅ Configuration loaded from config.json
[2025-09-05 21:56:03] INFO     🔄 Mode changed to: object_detection
[2025-09-05 21:56:03] INFO     🎯 Operating in Object Detection mode
[2025-09-05 21:56:03] INFO     🎭 Using mock camera for testing
[2025-09-05 21:56:03] INFO     Gemini AI client initialized successfully
[2025-09-05 21:56:03] INFO     Connecting to mock camera...
[2025-09-05 21:56:04] INFO     Mock camera connected successfully
[2025-09-05 21:56:04] INFO     Generated mock image with license plate: YZA890
[2025-09-05 21:56:04] INFO     Mock frame captured and saved: captured_images/mock_capture_20250905_215604.jpg
[2025-09-05 21:56:04] INFO     Processing image for Object Detection: captured_images/mock_capture_20250905_215604.jpg
[2025-09-05 21:56:06] INFO     API call successful: {'success': True, 'mode': 'object_detection', 'description': 'Blue vehicle with license plate YZA890 on gray road with dashed white lines.', 'word_count': 13, 'raw_response': 'Blue vehicle with license plate YZA890 on gray road with dashed white lines.', 'image_path': 'captured_images/mock_capture_20250905_215604.jpg'}
[2025-09-05 21:56:13] INFO     ✅ Configuration loaded from config.json
[2025-09-05 21:56:14] INFO     🔄 Mode changed to: license_plate
[2025-09-05 21:56:14] INFO     🎯 Operating in License Plate Detection mode
[2025-09-05 21:56:14] INFO     🎭 Using mock camera for testing
[2025-09-05 21:56:14] INFO     Gemini AI client initialized successfully
[2025-09-05 21:56:14] INFO     Connecting to mock camera...
[2025-09-05 21:56:15] INFO     Mock camera connected successfully
[2025-09-05 21:56:15] INFO     Generated mock image with license plate: VWX567
[2025-09-05 21:56:15] INFO     Mock frame captured and saved: captured_images/mock_capture_20250905_215615.jpg
[2025-09-05 21:56:15] INFO     Processing image for License Plate Detection: captured_images/mock_capture_20250905_215615.jpg
[2025-09-05 21:56:17] INFO     API call successful: {'success': True, 'license_plates': [{'plate_number': 'VWX567', 'location': 'Appears to be on the front of a blue vehicle', 'confidence': 'High', 'details': 'The license plate is in blue and white with the text "VWX567" visible.'}], 'plates_detected': 1, 'raw_response': '```\nLICENSE PLATES DETECTED: 1\n\nPlate 1:\n- Number: VWX567\n- Location: Appears to be on the front of a blue vehicle\n- Confidence: High\n- Details: The license plate is in blue and white with the text "VWX567" visible.\n```', 'image_path': 'captured_images/mock_capture_20250905_215615.jpg'}
[2025-09-05 21:56:17] INFO     License plates detected in captured_images/mock_capture_20250905_215615.jpg: [{'plate_number': 'VWX567', 'location': 'Appears to be on the front of a blue vehicle', 'confidence': 'High', 'details': 'The license plate is in blue and white with the text "VWX567" visible.'}]
[2025-09-05 21:57:43] INFO     ✅ Configuration loaded from config.json
[2025-09-05 21:57:45] INFO     🔄 Mode changed to: object_detection
[2025-09-05 21:57:45] INFO     🎯 Operating in Object Detection mode
[2025-09-05 21:57:45] INFO     📹 Initializing real RTSP camera
[2025-09-05 21:57:45] INFO     Gemini AI client initialized successfully
[2025-09-05 21:57:45] INFO     🔌 Connecting to RTSP camera: rtsp://***:***@*************:1935
[2025-09-05 21:57:45] WARNING  ⚠️ Failed to open RTSP stream (attempt 1)
[2025-09-05 21:57:45] INFO     🔄 Retry 2/3 in 2 seconds...
[2025-09-05 21:57:47] INFO     🔌 Connecting to RTSP camera: rtsp://***:***@*************:1935
[2025-09-05 21:57:48] WARNING  ⚠️ Failed to open RTSP stream (attempt 2)
[2025-09-05 21:57:48] INFO     🔄 Retry 3/3 in 4 seconds...
[2025-09-05 21:57:52] INFO     🔌 Connecting to RTSP camera: rtsp://***:***@*************:1935
[2025-09-05 21:57:52] WARNING  ⚠️ Failed to open RTSP stream (attempt 3)
[2025-09-05 21:57:52] ERROR    ❌ Failed to connect to RTSP camera after 3 attempts
[2025-09-05 21:57:55] INFO     ✅ Configuration loaded from config.json
[2025-09-05 21:57:56] INFO     🔄 Mode changed to: object_detection
[2025-09-05 21:57:56] INFO     🎯 Operating in Object Detection mode
[2025-09-05 21:57:56] INFO     📹 Initializing real RTSP camera
[2025-09-05 21:57:56] INFO     Gemini AI client initialized successfully
[2025-09-05 21:57:56] INFO     🔌 Connecting to RTSP camera: rtsp://***:***@*************:1935
[2025-09-05 21:57:56] WARNING  ⚠️ Failed to open RTSP stream (attempt 1)
[2025-09-05 21:57:56] INFO     🔄 Retry 2/3 in 2 seconds...
[2025-09-05 21:57:58] INFO     🔌 Connecting to RTSP camera: rtsp://***:***@*************:1935
[2025-09-05 21:57:58] WARNING  ⚠️ Failed to open RTSP stream (attempt 2)
[2025-09-05 21:57:58] INFO     🔄 Retry 3/3 in 4 seconds...
[2025-09-05 21:58:02] INFO     🔌 Connecting to RTSP camera: rtsp://***:***@*************:1935
[2025-09-05 21:58:03] WARNING  ⚠️ Failed to open RTSP stream (attempt 3)
[2025-09-05 21:58:03] ERROR    ❌ Failed to connect to RTSP camera after 3 attempts
[2025-09-05 21:58:19] INFO     ✅ Configuration loaded from config.json
[2025-09-05 21:58:20] INFO     🔄 Mode changed to: object_detection
[2025-09-05 21:58:20] INFO     🎯 Operating in Object Detection mode
[2025-09-05 21:58:20] INFO     📹 Initializing real RTSP camera
[2025-09-05 21:58:20] INFO     Gemini AI client initialized successfully
[2025-09-05 21:58:20] INFO     🔌 Connecting to RTSP camera: rtsp://***:***@*************:1935
[2025-09-05 21:58:20] WARNING  ⚠️ Failed to open RTSP stream (attempt 1)
[2025-09-05 21:58:20] INFO     🔄 Retry 2/3 in 2 seconds...
[2025-09-05 21:58:22] INFO     🔌 Connecting to RTSP camera: rtsp://***:***@*************:1935
[2025-09-05 21:58:22] WARNING  ⚠️ Failed to open RTSP stream (attempt 2)
[2025-09-05 21:58:22] INFO     🔄 Retry 3/3 in 4 seconds...
[2025-09-05 21:58:26] INFO     🔌 Connecting to RTSP camera: rtsp://***:***@*************:1935
[2025-09-05 21:58:26] WARNING  ⚠️ Failed to open RTSP stream (attempt 3)
[2025-09-05 21:58:26] ERROR    ❌ Failed to connect to RTSP camera after 3 attempts
[2025-09-05 21:59:48] INFO     ✅ Configuration loaded from config.json
[2025-09-05 21:59:50] INFO     🔄 Mode changed to: object_detection
[2025-09-05 21:59:50] INFO     🎯 Operating in Object Detection mode
[2025-09-05 21:59:50] INFO     📹 Initializing real RTSP camera
[2025-09-05 21:59:50] INFO     Gemini AI client initialized successfully
[2025-09-05 21:59:50] INFO     🔌 Connecting to RTSP camera: rtsp://***:***@*************:1935
[2025-09-05 21:59:52] INFO     ✅ Successfully connected to RTSP camera (retry 1, 2 successful reads)
[2025-09-05 21:59:52] INFO     📐 Frame size: (480, 640, 3)
[2025-09-05 21:59:52] INFO     ✅ Image captured and saved: captured_images/capture_20250905_215952.jpg
[2025-09-05 21:59:52] INFO     Image captured successfully: captured_images/capture_20250905_215952.jpg
[2025-09-05 21:59:52] INFO     Processing image for Object Detection: captured_images/capture_20250905_215952.jpg
[2025-09-05 21:59:52] ERROR    Error processing image captured_images/capture_20250905_215952.jpg: 400 INVALID_ARGUMENT. {'error': {'code': 400, 'message': 'API key expired. Please renew the API key.', 'status': 'INVALID_ARGUMENT', 'details': [{'@type': 'type.googleapis.com/google.rpc.ErrorInfo', 'reason': 'API_KEY_INVALID', 'domain': 'googleapis.com', 'metadata': {'service': 'generativelanguage.googleapis.com'}}, {'@type': 'type.googleapis.com/google.rpc.LocalizedMessage', 'locale': 'en-US', 'message': 'API key expired. Please renew the API key.'}]}}
[2025-09-05 21:59:52] ERROR    API call failed: Error processing image captured_images/capture_20250905_215952.jpg: 400 INVALID_ARGUMENT. {'error': {'code': 400, 'message': 'API key expired. Please renew the API key.', 'status': 'INVALID_ARGUMENT', 'details': [{'@type': 'type.googleapis.com/google.rpc.ErrorInfo', 'reason': 'API_KEY_INVALID', 'domain': 'googleapis.com', 'metadata': {'service': 'generativelanguage.googleapis.com'}}, {'@type': 'type.googleapis.com/google.rpc.LocalizedMessage', 'locale': 'en-US', 'message': 'API key expired. Please renew the API key.'}]}}
[2025-09-05 22:01:44] INFO     ✅ Configuration loaded from config.json
[2025-09-05 22:01:46] INFO     🔄 Mode changed to: object_detection
[2025-09-05 22:01:46] INFO     🎯 Operating in Object Detection mode
[2025-09-05 22:01:46] INFO     📹 Initializing real RTSP camera
[2025-09-05 22:01:46] INFO     Gemini AI client initialized successfully
[2025-09-05 22:01:46] INFO     🔌 Connecting to RTSP camera: rtsp://***:***@*************:1935
[2025-09-05 22:01:48] INFO     ✅ Successfully connected to RTSP camera (retry 1, 2 successful reads)
[2025-09-05 22:01:48] INFO     📐 Frame size: (480, 640, 3)
[2025-09-05 22:01:48] INFO     ✅ Image captured and saved: captured_images/capture_20250905_220148.jpg
[2025-09-05 22:01:48] INFO     Image captured successfully: captured_images/capture_20250905_220148.jpg
[2025-09-05 22:01:48] INFO     Processing image for Object Detection: captured_images/capture_20250905_220148.jpg
[2025-09-05 22:01:50] INFO     API call successful: {'success': True, 'mode': 'object_detection', 'description': 'A white lamp illuminates the corner with rounded architectural features, all in bright, minimalistic style.', 'word_count': 15, 'raw_response': 'A white lamp illuminates the corner with rounded architectural features, all in bright, minimalistic style.\n', 'image_path': 'captured_images/capture_20250905_220148.jpg'}
[2025-09-05 22:01:56] INFO     ✅ Configuration loaded from config.json
[2025-09-05 22:01:57] INFO     🔄 Mode changed to: license_plate
[2025-09-05 22:01:57] INFO     🎯 Operating in License Plate Detection mode
[2025-09-05 22:01:57] INFO     📹 Initializing real RTSP camera
[2025-09-05 22:01:57] INFO     Gemini AI client initialized successfully
[2025-09-05 22:01:57] INFO     🔌 Connecting to RTSP camera: rtsp://***:***@*************:1935
[2025-09-05 22:02:00] INFO     ✅ Successfully connected to RTSP camera (retry 1, 2 successful reads)
[2025-09-05 22:02:00] INFO     📐 Frame size: (480, 640, 3)
[2025-09-05 22:02:00] INFO     ✅ Image captured and saved: captured_images/capture_20250905_220200.jpg
[2025-09-05 22:02:00] INFO     Image captured successfully: captured_images/capture_20250905_220200.jpg
[2025-09-05 22:02:00] INFO     Processing image for License Plate Detection: captured_images/capture_20250905_220200.jpg
[2025-09-05 22:02:02] INFO     API call successful: {'success': True, 'license_plates': [], 'plates_detected': 0, 'raw_response': 'LICENSE PLATES DETECTED: 0\nNo license plates visible in this image.', 'image_path': 'captured_images/capture_20250905_220200.jpg'}
[2025-09-05 22:02:02] INFO     No license plates detected in captured_images/capture_20250905_220200.jpg
[2025-09-05 22:04:01] INFO     ✅ Configuration loaded from config.json
[2025-09-05 22:04:02] INFO     🔄 Mode changed to: license_plate
[2025-09-05 22:04:02] INFO     🎯 Operating in License Plate Detection mode
[2025-09-05 22:04:02] INFO     📹 Initializing real RTSP camera
[2025-09-05 22:04:02] INFO     Gemini AI client initialized successfully
[2025-09-05 22:04:02] INFO     🔌 Connecting to RTSP camera: rtsp://***:***@*************:1935
[2025-09-05 22:04:04] INFO     ✅ Successfully connected to RTSP camera (retry 1, 2 successful reads)
[2025-09-05 22:04:04] INFO     📐 Frame size: (480, 640, 3)
[2025-09-05 22:04:04] INFO     ✅ Image captured and saved: captured_images/capture_20250905_220404.jpg
[2025-09-05 22:04:04] INFO     Image captured successfully: captured_images/capture_20250905_220404.jpg
[2025-09-05 22:04:04] INFO     Processing image for License Plate Detection: captured_images/capture_20250905_220404.jpg
[2025-09-05 22:04:06] INFO     API call successful: {'success': True, 'license_plates': [], 'plates_detected': 0, 'raw_response': 'LICENSE PLATES DETECTED: 0\nNo license plates visible in this image.\n', 'image_path': 'captured_images/capture_20250905_220404.jpg'}
[2025-09-05 22:04:06] INFO     No license plates detected in captured_images/capture_20250905_220404.jpg
[2025-09-05 22:04:39] INFO     ✅ Configuration loaded from config.json
[2025-09-05 22:04:40] INFO     🔄 Mode changed to: object_detection
[2025-09-05 22:04:40] INFO     🎯 Operating in Object Detection mode
[2025-09-05 22:04:40] INFO     📹 Initializing real RTSP camera
[2025-09-05 22:04:40] INFO     Gemini AI client initialized successfully
[2025-09-05 22:04:40] INFO     🔌 Connecting to RTSP camera: rtsp://***:***@*************:1935
[2025-09-05 22:04:42] INFO     ✅ Successfully connected to RTSP camera (retry 1, 2 successful reads)
[2025-09-05 22:04:42] INFO     📐 Frame size: (480, 640, 3)
[2025-09-05 22:04:42] INFO     ✅ Image captured and saved: captured_images/capture_20250905_220442.jpg
[2025-09-05 22:04:42] INFO     Image captured successfully: captured_images/capture_20250905_220442.jpg
[2025-09-05 22:04:42] INFO     Processing image for Object Detection: captured_images/capture_20250905_220442.jpg
[2025-09-05 22:04:44] INFO     API call successful: {'success': True, 'mode': 'object_detection', 'description': "Person's face is visible, possibly looking down, in a brightly lit indoor space.", 'word_count': 13, 'raw_response': "Person's face is visible, possibly looking down, in a brightly lit indoor space.\n", 'image_path': 'captured_images/capture_20250905_220442.jpg'}
[2025-09-05 22:06:02] INFO     ✅ Configuration loaded from config.json
[2025-09-05 22:06:03] INFO     🔄 Mode changed to: object_detection
[2025-09-05 22:06:03] INFO     🎯 Operating in Object Detection mode
[2025-09-05 22:06:03] INFO     📹 Initializing real RTSP camera
[2025-09-05 22:06:03] INFO     Gemini AI client initialized successfully
[2025-09-05 22:06:03] INFO     🔌 Connecting to RTSP camera: rtsp://***:***@*************:1935
[2025-09-05 22:06:06] INFO     ✅ Successfully connected to RTSP camera (retry 1, 2 successful reads)
[2025-09-05 22:06:06] INFO     📐 Frame size: (480, 640, 3)
[2025-09-05 22:06:06] INFO     ✅ Image captured and saved: captured_images/capture_20250905_220606.jpg
[2025-09-05 22:06:06] INFO     Image captured successfully: captured_images/capture_20250905_220606.jpg
[2025-09-05 22:06:06] INFO     Processing image for Object Detection: captured_images/capture_20250905_220606.jpg
[2025-09-05 22:06:08] INFO     API call successful: {'success': True, 'mode': 'object_detection', 'description': 'Interior scene shows tile floor, kitchen area, and decorative light fixtures.', 'word_count': 11, 'raw_response': 'Interior scene shows tile floor, kitchen area, and decorative light fixtures.\n', 'image_path': 'captured_images/capture_20250905_220606.jpg'}
[2025-09-05 22:06:20] INFO     ✅ Configuration loaded from config.json
[2025-09-05 22:06:21] INFO     🔄 Mode changed to: object_detection
[2025-09-05 22:06:21] INFO     🎯 Operating in Object Detection mode
[2025-09-05 22:06:21] INFO     📹 Initializing real RTSP camera
[2025-09-05 22:06:21] INFO     Gemini AI client initialized successfully
[2025-09-05 22:06:21] INFO     🤖 Testing AI processor connection...
[2025-09-05 22:06:22] INFO     Gemini API connection test successful. Response: API connection successful
...
[2025-09-05 22:06:22] INFO     📷 Testing camera connection...
[2025-09-05 22:06:22] INFO     🔄 Attempting to connect to real RTSP camera...
[2025-09-05 22:06:22] INFO     🔌 Connecting to RTSP camera: rtsp://***:***@*************:1935
[2025-09-05 22:06:25] INFO     ✅ Successfully connected to RTSP camera (retry 1, 2 successful reads)
[2025-09-05 22:06:25] INFO     📐 Frame size: (480, 640, 3)
[2025-09-05 22:06:25] INFO     ✅ Real RTSP camera connected successfully!
[2025-09-05 22:06:25] INFO     ⏰ Scheduling capture every 1 minute(s)
[2025-09-05 22:07:25] INFO     ✅ Image captured and saved: captured_images/capture_20250905_220725.jpg
[2025-09-05 22:07:25] INFO     Image captured successfully: captured_images/capture_20250905_220725.jpg
[2025-09-05 22:07:25] INFO     📷 Image captured: capture_20250905_220725.jpg
[2025-09-05 22:07:25] INFO     🤖 Processing with AI (Object Detection)...
[2025-09-05 22:07:25] INFO     Processing image for Object Detection: captured_images/capture_20250905_220725.jpg
[2025-09-05 22:07:27] INFO     API call successful: {'success': True, 'mode': 'object_detection', 'description': 'Overturned plant and broken mirror inside, city buildings visible outside window.', 'word_count': 11, 'raw_response': 'Overturned plant and broken mirror inside, city buildings visible outside window.\n', 'image_path': 'captured_images/capture_20250905_220725.jpg'}
[2025-09-05 22:07:27] INFO     👁️  Scene: Overturned plant and broken mirror inside, city buildings visible outside window. (11 words)
[2025-09-05 22:07:27] INFO     SCENE: Overturned plant and broken mirror inside, city buildings visible outside window. | 11 words | capture_20250905_220725.jpg
[2025-09-05 22:08:27] INFO     ✅ Image captured and saved: captured_images/capture_20250905_220827.jpg
[2025-09-05 22:08:27] INFO     Image captured successfully: captured_images/capture_20250905_220827.jpg
[2025-09-05 22:08:27] INFO     📷 Image captured: capture_20250905_220827.jpg
[2025-09-05 22:08:27] INFO     🤖 Processing with AI (Object Detection)...
[2025-09-05 22:08:27] INFO     Processing image for Object Detection: captured_images/capture_20250905_220827.jpg
[2025-09-05 22:08:30] INFO     API call successful: {'success': True, 'mode': 'object_detection', 'description': 'View from window: City buildings at night, indoor plant and objects visible.', 'word_count': 12, 'raw_response': 'View from window: City buildings at night, indoor plant and objects visible.\n', 'image_path': 'captured_images/capture_20250905_220827.jpg'}
[2025-09-05 22:08:30] INFO     👁️  Scene: View from window: City buildings at night, indoor plant and objects visible. (12 words)
[2025-09-05 22:08:30] INFO     SCENE: View from window: City buildings at night, indoor plant and objects visible. | 12 words | capture_20250905_220827.jpg
[2025-09-05 22:09:30] INFO     ✅ Image captured and saved: captured_images/capture_20250905_220930.jpg
[2025-09-05 22:09:30] INFO     Image captured successfully: captured_images/capture_20250905_220930.jpg
[2025-09-05 22:09:30] INFO     📷 Image captured: capture_20250905_220930.jpg
[2025-09-05 22:09:30] INFO     🤖 Processing with AI (Object Detection)...
[2025-09-05 22:09:30] INFO     Processing image for Object Detection: captured_images/capture_20250905_220930.jpg
[2025-09-05 22:09:32] INFO     API call successful: {'success': True, 'mode': 'object_detection', 'description': 'Potted plant sits inside, overlooking city buildings at night through window.', 'word_count': 11, 'raw_response': 'Potted plant sits inside, overlooking city buildings at night through window.\n', 'image_path': 'captured_images/capture_20250905_220930.jpg'}
[2025-09-05 22:09:32] INFO     👁️  Scene: Potted plant sits inside, overlooking city buildings at night through window. (11 words)
[2025-09-05 22:09:32] INFO     SCENE: Potted plant sits inside, overlooking city buildings at night through window. | 11 words | capture_20250905_220930.jpg
[2025-09-05 22:10:33] INFO     ✅ Image captured and saved: captured_images/capture_20250905_221033.jpg
[2025-09-05 22:10:33] INFO     Image captured successfully: captured_images/capture_20250905_221033.jpg
[2025-09-05 22:10:33] INFO     📷 Image captured: capture_20250905_221033.jpg
[2025-09-05 22:10:33] INFO     🤖 Processing with AI (Object Detection)...
[2025-09-05 22:10:33] INFO     Processing image for Object Detection: captured_images/capture_20250905_221033.jpg
[2025-09-05 22:10:35] INFO     API call successful: {'success': True, 'mode': 'object_detection', 'description': 'Indoor scene with broken plant pot, looking out at city buildings at night.', 'word_count': 13, 'raw_response': 'Indoor scene with broken plant pot, looking out at city buildings at night.\n', 'image_path': 'captured_images/capture_20250905_221033.jpg'}
[2025-09-05 22:10:35] INFO     👁️  Scene: Indoor scene with broken plant pot, looking out at city buildings at night. (13 words)
[2025-09-05 22:10:35] INFO     SCENE: Indoor scene with broken plant pot, looking out at city buildings at night. | 13 words | capture_20250905_221033.jpg
[2025-09-05 22:11:06] INFO     ✅ Configuration loaded from config.json
[2025-09-05 22:11:08] INFO     🔄 Mode changed to: object_detection
[2025-09-05 22:11:08] INFO     🎯 Operating in Object Detection mode
[2025-09-05 22:11:08] INFO     📹 Initializing real RTSP camera
[2025-09-05 22:11:08] INFO     Gemini AI client initialized successfully
[2025-09-05 22:11:08] INFO     🔌 Connecting to RTSP camera: rtsp://***:***@*************:1935
[2025-09-05 22:11:10] INFO     ✅ Successfully connected to RTSP camera (retry 1, 2 successful reads)
[2025-09-05 22:11:10] INFO     📐 Frame size: (480, 640, 3)
[2025-09-05 22:11:10] INFO     ✅ Image captured and saved: captured_images/capture_20250905_221110.jpg
[2025-09-05 22:11:10] INFO     Image captured successfully: captured_images/capture_20250905_221110.jpg
[2025-09-05 22:11:10] INFO     Processing image for Object Detection: captured_images/capture_20250905_221110.jpg
[2025-09-05 22:11:13] INFO     API call successful: {'success': True, 'mode': 'object_detection', 'description': 'Leather chair next to a window with wood floors and a wall air conditioner.', 'word_count': 14, 'raw_response': 'Leather chair next to a window with wood floors and a wall air conditioner.\n', 'image_path': 'captured_images/capture_20250905_221110.jpg'}
[2025-09-05 22:11:35] INFO     ✅ Image captured and saved: captured_images/capture_20250905_221135.jpg
[2025-09-05 22:11:35] INFO     Image captured successfully: captured_images/capture_20250905_221135.jpg
[2025-09-05 22:11:35] INFO     📷 Image captured: capture_20250905_221135.jpg
[2025-09-05 22:11:35] INFO     🤖 Processing with AI (Object Detection)...
[2025-09-05 22:11:35] INFO     Processing image for Object Detection: captured_images/capture_20250905_221135.jpg
[2025-09-05 22:11:38] INFO     API call successful: {'success': True, 'mode': 'object_detection', 'description': 'Fallen pot with plant inside a room, city view outside window.', 'word_count': 11, 'raw_response': 'Fallen pot with plant inside a room, city view outside window.\n', 'image_path': 'captured_images/capture_20250905_221135.jpg'}
[2025-09-05 22:11:38] INFO     👁️  Scene: Fallen pot with plant inside a room, city view outside window. (11 words)
[2025-09-05 22:11:38] INFO     SCENE: Fallen pot with plant inside a room, city view outside window. | 11 words | capture_20250905_221135.jpg
[2025-09-05 22:12:38] INFO     ✅ Image captured and saved: captured_images/capture_20250905_221238.jpg
[2025-09-05 22:12:38] INFO     Image captured successfully: captured_images/capture_20250905_221238.jpg
[2025-09-05 22:12:38] INFO     📷 Image captured: capture_20250905_221238.jpg
[2025-09-05 22:12:38] INFO     🤖 Processing with AI (Object Detection)...
[2025-09-05 22:12:38] INFO     Processing image for Object Detection: captured_images/capture_20250905_221238.jpg
[2025-09-05 22:12:41] INFO     API call successful: {'success': True, 'mode': 'object_detection', 'description': 'Overturned pot with plants, seen through window with city lights at night.', 'word_count': 12, 'raw_response': 'Overturned pot with plants, seen through window with city lights at night.\n', 'image_path': 'captured_images/capture_20250905_221238.jpg'}
[2025-09-05 22:12:41] INFO     👁️  Scene: Overturned pot with plants, seen through window with city lights at night. (12 words)
[2025-09-05 22:12:41] INFO     SCENE: Overturned pot with plants, seen through window with city lights at night. | 12 words | capture_20250905_221238.jpg
[2025-09-05 22:13:41] INFO     ✅ Image captured and saved: captured_images/capture_20250905_221341.jpg
[2025-09-05 22:13:41] INFO     Image captured successfully: captured_images/capture_20250905_221341.jpg
[2025-09-05 22:13:41] INFO     📷 Image captured: capture_20250905_221341.jpg
[2025-09-05 22:13:41] INFO     🤖 Processing with AI (Object Detection)...
[2025-09-05 22:13:41] INFO     Processing image for Object Detection: captured_images/capture_20250905_221341.jpg
[2025-09-05 22:13:43] INFO     API call successful: {'success': True, 'mode': 'object_detection', 'description': 'Potted plant fallen over inside, with a night cityscape seen through the window.', 'word_count': 13, 'raw_response': 'Potted plant fallen over inside, with a night cityscape seen through the window.\n', 'image_path': 'captured_images/capture_20250905_221341.jpg'}
[2025-09-05 22:13:43] INFO     👁️  Scene: Potted plant fallen over inside, with a night cityscape seen through the window. (13 words)
[2025-09-05 22:13:43] INFO     SCENE: Potted plant fallen over inside, with a night cityscape seen through the window. | 13 words | capture_20250905_221341.jpg
[2025-09-05 22:14:44] INFO     ✅ Image captured and saved: captured_images/capture_20250905_221444.jpg
[2025-09-05 22:14:44] INFO     Image captured successfully: captured_images/capture_20250905_221444.jpg
[2025-09-05 22:14:44] INFO     📷 Image captured: capture_20250905_221444.jpg
[2025-09-05 22:14:44] INFO     🤖 Processing with AI (Object Detection)...
[2025-09-05 22:14:44] INFO     Processing image for Object Detection: captured_images/capture_20250905_221444.jpg
[2025-09-05 22:14:46] INFO     API call successful: {'success': True, 'mode': 'object_detection', 'description': 'Potted plant is knocked over indoors with a night cityscape seen through window.', 'word_count': 13, 'raw_response': 'Potted plant is knocked over indoors with a night cityscape seen through window.\n', 'image_path': 'captured_images/capture_20250905_221444.jpg'}
[2025-09-05 22:14:46] INFO     👁️  Scene: Potted plant is knocked over indoors with a night cityscape seen through window. (13 words)
[2025-09-05 22:14:46] INFO     SCENE: Potted plant is knocked over indoors with a night cityscape seen through window. | 13 words | capture_20250905_221444.jpg
[2025-09-05 22:15:46] INFO     ✅ Image captured and saved: captured_images/capture_20250905_221546.jpg
[2025-09-05 22:15:46] INFO     Image captured successfully: captured_images/capture_20250905_221546.jpg
[2025-09-05 22:15:46] INFO     📷 Image captured: capture_20250905_221546.jpg
[2025-09-05 22:15:46] INFO     🤖 Processing with AI (Object Detection)...
[2025-09-05 22:15:46] INFO     Processing image for Object Detection: captured_images/capture_20250905_221546.jpg
[2025-09-05 22:15:49] INFO     API call successful: {'success': True, 'mode': 'object_detection', 'description': 'Fallen flowerpot inside next to window overlooking city at night.', 'word_count': 10, 'raw_response': 'Fallen flowerpot inside next to window overlooking city at night.\n', 'image_path': 'captured_images/capture_20250905_221546.jpg'}
[2025-09-05 22:15:49] INFO     👁️  Scene: Fallen flowerpot inside next to window overlooking city at night. (10 words)
[2025-09-05 22:15:49] INFO     SCENE: Fallen flowerpot inside next to window overlooking city at night. | 10 words | capture_20250905_221546.jpg
[2025-09-05 22:16:49] INFO     ✅ Image captured and saved: captured_images/capture_20250905_221649.jpg
[2025-09-05 22:16:49] INFO     Image captured successfully: captured_images/capture_20250905_221649.jpg
[2025-09-05 22:16:49] INFO     📷 Image captured: capture_20250905_221649.jpg
[2025-09-05 22:16:49] INFO     🤖 Processing with AI (Object Detection)...
[2025-09-05 22:16:49] INFO     Processing image for Object Detection: captured_images/capture_20250905_221649.jpg
[2025-09-05 22:16:51] INFO     API call successful: {'success': True, 'mode': 'object_detection', 'description': 'Fallen potted plant inside with cityscape view outside window.', 'word_count': 9, 'raw_response': 'Fallen potted plant inside with cityscape view outside window.\n', 'image_path': 'captured_images/capture_20250905_221649.jpg'}
[2025-09-05 22:16:51] INFO     👁️  Scene: Fallen potted plant inside with cityscape view outside window. (9 words)
[2025-09-05 22:16:51] INFO     SCENE: Fallen potted plant inside with cityscape view outside window. | 9 words | capture_20250905_221649.jpg
[2025-09-05 22:17:52] INFO     ✅ Image captured and saved: captured_images/capture_20250905_221752.jpg
[2025-09-05 22:17:52] INFO     Image captured successfully: captured_images/capture_20250905_221752.jpg
[2025-09-05 22:17:52] INFO     📷 Image captured: capture_20250905_221752.jpg
[2025-09-05 22:17:52] INFO     🤖 Processing with AI (Object Detection)...
[2025-09-05 22:17:52] INFO     Processing image for Object Detection: captured_images/capture_20250905_221752.jpg
[2025-09-05 22:17:54] INFO     API call successful: {'success': True, 'mode': 'object_detection', 'description': 'Upturned potted plant indoor, with city buildings outside through window, viewed from above.', 'word_count': 13, 'raw_response': 'Upturned potted plant indoor, with city buildings outside through window, viewed from above.\n', 'image_path': 'captured_images/capture_20250905_221752.jpg'}
[2025-09-05 22:17:54] INFO     👁️  Scene: Upturned potted plant indoor, with city buildings outside through window, viewed from above. (13 words)
[2025-09-05 22:17:54] INFO     SCENE: Upturned potted plant indoor, with city buildings outside through window, viewed from above. | 13 words | capture_20250905_221752.jpg
[2025-09-05 22:18:05] INFO     ✅ Configuration loaded from config.json
[2025-09-05 22:18:06] INFO     🎯 Operating in License Plate Detection mode
[2025-09-05 22:18:06] INFO     📹 Initializing real RTSP camera
[2025-09-05 22:18:06] INFO     Gemini AI client initialized successfully
[2025-09-05 22:18:06] INFO     ✅ AI Vision Monitor initialized for web interface
[2025-09-05 22:18:19] INFO     ✅ Configuration loaded from config.json
[2025-09-05 22:18:54] INFO     ✅ Image captured and saved: captured_images/capture_20250905_221854.jpg
[2025-09-05 22:18:54] INFO     Image captured successfully: captured_images/capture_20250905_221854.jpg
[2025-09-05 22:18:54] INFO     📷 Image captured: capture_20250905_221854.jpg
[2025-09-05 22:18:54] INFO     🤖 Processing with AI (Object Detection)...
[2025-09-05 22:18:54] INFO     Processing image for Object Detection: captured_images/capture_20250905_221854.jpg
[2025-09-05 22:18:56] INFO     API call successful: {'success': True, 'mode': 'object_detection', 'description': 'Potted plant fallen over inside near window, cityscape visible outside.', 'word_count': 10, 'raw_response': 'Potted plant fallen over inside near window, cityscape visible outside.\n', 'image_path': 'captured_images/capture_20250905_221854.jpg'}
[2025-09-05 22:18:56] INFO     👁️  Scene: Potted plant fallen over inside near window, cityscape visible outside. (10 words)
[2025-09-05 22:18:56] INFO     SCENE: Potted plant fallen over inside near window, cityscape visible outside. | 10 words | capture_20250905_221854.jpg
[2025-09-05 22:19:57] INFO     ✅ Image captured and saved: captured_images/capture_20250905_221957.jpg
[2025-09-05 22:19:57] INFO     Image captured successfully: captured_images/capture_20250905_221957.jpg
[2025-09-05 22:19:57] INFO     📷 Image captured: capture_20250905_221957.jpg
[2025-09-05 22:19:57] INFO     🤖 Processing with AI (Object Detection)...
[2025-09-05 22:19:57] INFO     Processing image for Object Detection: captured_images/capture_20250905_221957.jpg
[2025-09-05 22:19:59] INFO     API call successful: {'success': True, 'mode': 'object_detection', 'description': 'Fallen potted plant indoors; outside the window is a city scene at night.', 'word_count': 13, 'raw_response': 'Fallen potted plant indoors; outside the window is a city scene at night.\n', 'image_path': 'captured_images/capture_20250905_221957.jpg'}
[2025-09-05 22:19:59] INFO     👁️  Scene: Fallen potted plant indoors; outside the window is a city scene at night. (13 words)
[2025-09-05 22:19:59] INFO     SCENE: Fallen potted plant indoors; outside the window is a city scene at night. | 13 words | capture_20250905_221957.jpg
[2025-09-05 22:20:21] INFO     ✅ Configuration loaded from config.json
[2025-09-05 22:20:21] INFO     🌐 Starting License Plate Recognition Web Interface...
[2025-09-05 22:20:23] INFO     🎯 Operating in License Plate Detection mode
[2025-09-05 22:20:23] INFO     📹 Initializing real RTSP camera
[2025-09-05 22:20:23] INFO     Gemini AI client initialized successfully
[2025-09-05 22:20:23] INFO     ✅ AI Vision Monitor initialized for web interface
[2025-09-05 22:20:59] INFO     ✅ Image captured and saved: captured_images/capture_20250905_222059.jpg
[2025-09-05 22:20:59] INFO     Image captured successfully: captured_images/capture_20250905_222059.jpg
[2025-09-05 22:20:59] INFO     📷 Image captured: capture_20250905_222059.jpg
[2025-09-05 22:20:59] INFO     🤖 Processing with AI (Object Detection)...
[2025-09-05 22:20:59] INFO     Processing image for Object Detection: captured_images/capture_20250905_222059.jpg
[2025-09-05 22:21:02] INFO     API call successful: {'success': True, 'mode': 'object_detection', 'description': 'Apartment view with tipped-over plant pot looking out onto city buildings at night.', 'word_count': 13, 'raw_response': 'Apartment view with tipped-over plant pot looking out onto city buildings at night.\n', 'image_path': 'captured_images/capture_20250905_222059.jpg'}
[2025-09-05 22:21:02] INFO     👁️  Scene: Apartment view with tipped-over plant pot looking out onto city buildings at night. (13 words)
[2025-09-05 22:21:02] INFO     SCENE: Apartment view with tipped-over plant pot looking out onto city buildings at night. | 13 words | capture_20250905_222059.jpg
[2025-09-05 22:21:05] INFO     ✅ Configuration loaded from config.json
[2025-09-05 22:21:05] INFO     🌐 Starting License Plate Recognition Web Interface...
[2025-09-05 22:21:06] INFO     🎯 Operating in License Plate Detection mode
[2025-09-05 22:21:06] INFO     📹 Initializing real RTSP camera
[2025-09-05 22:21:06] INFO     Gemini AI client initialized successfully
[2025-09-05 22:21:06] INFO     ✅ AI Vision Monitor initialized for web interface
[2025-09-05 22:21:23] INFO     ✅ Configuration loaded from config.json
[2025-09-05 22:21:24] INFO     🌐 Starting License Plate Recognition Web Interface...
[2025-09-05 22:21:25] INFO     🎯 Operating in License Plate Detection mode
[2025-09-05 22:21:25] INFO     📹 Initializing real RTSP camera
[2025-09-05 22:21:25] INFO     Gemini AI client initialized successfully
[2025-09-05 22:21:25] INFO     ✅ AI Vision Monitor initialized for web interface
[2025-09-05 22:22:02] INFO     ✅ Image captured and saved: captured_images/capture_20250905_222202.jpg
[2025-09-05 22:22:02] INFO     Image captured successfully: captured_images/capture_20250905_222202.jpg
[2025-09-05 22:22:02] INFO     📷 Image captured: capture_20250905_222202.jpg
[2025-09-05 22:22:02] INFO     🤖 Processing with AI (Object Detection)...
[2025-09-05 22:22:02] INFO     Processing image for Object Detection: captured_images/capture_20250905_222202.jpg
[2025-09-05 22:22:04] INFO     API call successful: {'success': True, 'mode': 'object_detection', 'description': 'Potted plant lies on floor, cityscape reflection in window at night.', 'word_count': 11, 'raw_response': 'Potted plant lies on floor, cityscape reflection in window at night.\n', 'image_path': 'captured_images/capture_20250905_222202.jpg'}
[2025-09-05 22:22:04] INFO     👁️  Scene: Potted plant lies on floor, cityscape reflection in window at night. (11 words)
[2025-09-05 22:22:04] INFO     SCENE: Potted plant lies on floor, cityscape reflection in window at night. | 11 words | capture_20250905_222202.jpg
[2025-09-05 22:22:57] INFO     Received signal 2, shutting down gracefully...
[2025-09-05 22:22:57] INFO     🛑 Stopping License Plate Monitoring System...
[2025-09-05 22:22:57] INFO     🔌 Disconnected from RTSP camera
[2025-09-05 22:22:57] INFO     ✅ License Plate Monitoring System stopped
[2025-09-05 22:23:01] INFO     Received signal 2, shutting down gracefully...
[2025-09-05 22:23:01] INFO     🛑 Stopping License Plate Monitoring System...
[2025-09-05 22:23:01] INFO     🔌 Disconnected from RTSP camera
[2025-09-05 22:23:01] INFO     ✅ License Plate Monitoring System stopped
[2025-09-05 22:23:02] INFO     Received signal 2, shutting down gracefully...
[2025-09-05 22:23:02] INFO     🛑 Stopping License Plate Monitoring System...
[2025-09-05 22:23:02] INFO     🔌 Disconnected from RTSP camera
[2025-09-05 22:23:02] INFO     ✅ License Plate Monitoring System stopped
[2025-09-05 22:23:02] INFO     Received signal 2, shutting down gracefully...
[2025-09-05 22:23:02] INFO     🛑 Stopping License Plate Monitoring System...
[2025-09-05 22:23:02] INFO     🔌 Disconnected from RTSP camera
[2025-09-05 22:23:02] INFO     ✅ License Plate Monitoring System stopped
[2025-09-05 22:23:03] INFO     Received signal 2, shutting down gracefully...
[2025-09-05 22:23:03] INFO     🛑 Stopping License Plate Monitoring System...
[2025-09-05 22:23:03] INFO     🔌 Disconnected from RTSP camera
[2025-09-05 22:23:03] INFO     ✅ License Plate Monitoring System stopped
[2025-09-05 22:23:03] INFO     Received signal 2, shutting down gracefully...
[2025-09-05 22:23:03] INFO     🛑 Stopping License Plate Monitoring System...
[2025-09-05 22:23:03] INFO     🔌 Disconnected from RTSP camera
[2025-09-05 22:23:03] INFO     ✅ License Plate Monitoring System stopped
[2025-09-05 22:23:03] INFO     Received signal 2, shutting down gracefully...
[2025-09-05 22:23:03] INFO     🛑 Stopping License Plate Monitoring System...
[2025-09-05 22:23:03] INFO     🔌 Disconnected from RTSP camera
[2025-09-05 22:23:03] INFO     ✅ License Plate Monitoring System stopped
[2025-09-05 22:23:04] INFO     ✅ Image captured and saved: captured_images/capture_20250905_222304.jpg
[2025-09-05 22:23:04] INFO     Image captured successfully: captured_images/capture_20250905_222304.jpg
[2025-09-05 22:23:04] INFO     📷 Image captured: capture_20250905_222304.jpg
[2025-09-05 22:23:04] INFO     🤖 Processing with AI (Object Detection)...
[2025-09-05 22:23:04] INFO     Processing image for Object Detection: captured_images/capture_20250905_222304.jpg
[2025-09-05 22:23:14] INFO     ✅ Configuration loaded from config.json
[2025-09-05 22:23:49] INFO     ✅ Configuration loaded from config.json
[2025-09-05 22:23:58] INFO     ✅ Configuration loaded from config.json
[2025-09-05 22:24:00] INFO     🎯 Operating in License Plate Detection mode
[2025-09-05 22:24:00] INFO     📹 Initializing real RTSP camera
[2025-09-05 22:24:00] INFO     Gemini AI client initialized successfully
[2025-09-05 22:24:00] INFO     ✅ AI Vision Monitor initialized for web interface
[2025-09-05 22:24:17] INFO     ✅ Configuration loaded from config.json
[2025-09-05 22:24:17] INFO     🌐 Starting License Plate Recognition Web Interface...
[2025-09-05 22:24:18] INFO     🎯 Operating in License Plate Detection mode
[2025-09-05 22:24:18] INFO     📹 Initializing real RTSP camera
[2025-09-05 22:24:18] INFO     Gemini AI client initialized successfully
[2025-09-05 22:24:18] INFO     ✅ AI Vision Monitor initialized for web interface
[2025-09-05 22:24:28] INFO     📸 Manual capture initiated from web interface
[2025-09-05 22:24:28] WARNING  Camera disconnected, attempting to reconnect...
[2025-09-05 22:24:28] INFO     🔌 Connecting to RTSP camera: rtsp://***:***@*************:1935
[2025-09-05 22:24:28] WARNING  ⚠️ Failed to open RTSP stream (attempt 1)
[2025-09-05 22:24:28] INFO     🔄 Retry 2/3 in 2 seconds...
[2025-09-05 22:24:30] INFO     🔌 Connecting to RTSP camera: rtsp://***:***@*************:1935
[2025-09-05 22:24:31] WARNING  ⚠️ Failed to open RTSP stream (attempt 2)
[2025-09-05 22:24:31] INFO     🔄 Retry 3/3 in 4 seconds...
[2025-09-05 22:24:35] INFO     🔌 Connecting to RTSP camera: rtsp://***:***@*************:1935
[2025-09-05 22:24:35] WARNING  ⚠️ Failed to open RTSP stream (attempt 3)
[2025-09-05 22:24:35] ERROR    ❌ Failed to connect to RTSP camera after 3 attempts
[2025-09-05 22:24:35] ERROR    ❌ Failed to reconnect to camera
[2025-09-05 22:24:45] INFO     📸 Manual capture initiated from web interface
[2025-09-05 22:24:45] WARNING  Camera disconnected, attempting to reconnect...
[2025-09-05 22:24:45] INFO     🔌 Connecting to RTSP camera: rtsp://***:***@*************:1935
[2025-09-05 22:24:49] INFO     ✅ Successfully connected to RTSP camera (retry 1, 2 successful reads)
[2025-09-05 22:24:49] INFO     📐 Frame size: (480, 640, 3)
[2025-09-05 22:24:49] INFO     ✅ Image captured and saved: captured_images/capture_20250905_222449.jpg
[2025-09-05 22:24:49] INFO     Image captured successfully: captured_images/capture_20250905_222449.jpg
[2025-09-05 22:24:49] INFO     📷 Image captured: capture_20250905_222449.jpg
[2025-09-05 22:24:49] INFO     🤖 Processing with AI (License Plate Detection)...
[2025-09-05 22:24:49] INFO     Processing image for License Plate Detection: captured_images/capture_20250905_222449.jpg
[2025-09-05 22:24:49] ERROR    Error processing image captured_images/capture_20250905_222449.jpg: 400 INVALID_ARGUMENT. {'error': {'code': 400, 'message': 'API key expired. Please renew the API key.', 'status': 'INVALID_ARGUMENT', 'details': [{'@type': 'type.googleapis.com/google.rpc.ErrorInfo', 'reason': 'API_KEY_INVALID', 'domain': 'googleapis.com', 'metadata': {'service': 'generativelanguage.googleapis.com'}}, {'@type': 'type.googleapis.com/google.rpc.LocalizedMessage', 'locale': 'en-US', 'message': 'API key expired. Please renew the API key.'}]}}
[2025-09-05 22:24:49] ERROR    API call failed: Error processing image captured_images/capture_20250905_222449.jpg: 400 INVALID_ARGUMENT. {'error': {'code': 400, 'message': 'API key expired. Please renew the API key.', 'status': 'INVALID_ARGUMENT', 'details': [{'@type': 'type.googleapis.com/google.rpc.ErrorInfo', 'reason': 'API_KEY_INVALID', 'domain': 'googleapis.com', 'metadata': {'service': 'generativelanguage.googleapis.com'}}, {'@type': 'type.googleapis.com/google.rpc.LocalizedMessage', 'locale': 'en-US', 'message': 'API key expired. Please renew the API key.'}]}}
[2025-09-05 22:24:49] ERROR    ❌ AI processing failed: Error processing image captured_images/capture_20250905_222449.jpg: 400 INVALID_ARGUMENT. {'error': {'code': 400, 'message': 'API key expired. Please renew the API key.', 'status': 'INVALID_ARGUMENT', 'details': [{'@type': 'type.googleapis.com/google.rpc.ErrorInfo', 'reason': 'API_KEY_INVALID', 'domain': 'googleapis.com', 'metadata': {'service': 'generativelanguage.googleapis.com'}}, {'@type': 'type.googleapis.com/google.rpc.LocalizedMessage', 'locale': 'en-US', 'message': 'API key expired. Please renew the API key.'}]}}
[2025-09-05 22:24:53] INFO     📸 Manual capture initiated from web interface
[2025-09-05 22:24:53] INFO     ✅ Image captured and saved: captured_images/capture_20250905_222453.jpg
[2025-09-05 22:24:53] INFO     Image captured successfully: captured_images/capture_20250905_222453.jpg
[2025-09-05 22:24:53] INFO     📷 Image captured: capture_20250905_222453.jpg
[2025-09-05 22:24:53] INFO     🤖 Processing with AI (License Plate Detection)...
[2025-09-05 22:24:53] INFO     Processing image for License Plate Detection: captured_images/capture_20250905_222453.jpg
[2025-09-05 22:24:54] ERROR    Error processing image captured_images/capture_20250905_222453.jpg: 400 INVALID_ARGUMENT. {'error': {'code': 400, 'message': 'API key expired. Please renew the API key.', 'status': 'INVALID_ARGUMENT', 'details': [{'@type': 'type.googleapis.com/google.rpc.ErrorInfo', 'reason': 'API_KEY_INVALID', 'domain': 'googleapis.com', 'metadata': {'service': 'generativelanguage.googleapis.com'}}, {'@type': 'type.googleapis.com/google.rpc.LocalizedMessage', 'locale': 'en-US', 'message': 'API key expired. Please renew the API key.'}]}}
[2025-09-05 22:24:54] ERROR    API call failed: Error processing image captured_images/capture_20250905_222453.jpg: 400 INVALID_ARGUMENT. {'error': {'code': 400, 'message': 'API key expired. Please renew the API key.', 'status': 'INVALID_ARGUMENT', 'details': [{'@type': 'type.googleapis.com/google.rpc.ErrorInfo', 'reason': 'API_KEY_INVALID', 'domain': 'googleapis.com', 'metadata': {'service': 'generativelanguage.googleapis.com'}}, {'@type': 'type.googleapis.com/google.rpc.LocalizedMessage', 'locale': 'en-US', 'message': 'API key expired. Please renew the API key.'}]}}
[2025-09-05 22:24:54] ERROR    ❌ AI processing failed: Error processing image captured_images/capture_20250905_222453.jpg: 400 INVALID_ARGUMENT. {'error': {'code': 400, 'message': 'API key expired. Please renew the API key.', 'status': 'INVALID_ARGUMENT', 'details': [{'@type': 'type.googleapis.com/google.rpc.ErrorInfo', 'reason': 'API_KEY_INVALID', 'domain': 'googleapis.com', 'metadata': {'service': 'generativelanguage.googleapis.com'}}, {'@type': 'type.googleapis.com/google.rpc.LocalizedMessage', 'locale': 'en-US', 'message': 'API key expired. Please renew the API key.'}]}}
[2025-09-05 22:24:57] INFO     📸 Manual capture initiated from web interface
[2025-09-05 22:24:57] INFO     ✅ Image captured and saved: captured_images/capture_20250905_222457.jpg
[2025-09-05 22:24:57] INFO     Image captured successfully: captured_images/capture_20250905_222457.jpg
[2025-09-05 22:24:57] INFO     📷 Image captured: capture_20250905_222457.jpg
[2025-09-05 22:24:57] INFO     🤖 Processing with AI (License Plate Detection)...
[2025-09-05 22:24:57] INFO     Processing image for License Plate Detection: captured_images/capture_20250905_222457.jpg
[2025-09-05 22:24:57] ERROR    Error processing image captured_images/capture_20250905_222457.jpg: 400 INVALID_ARGUMENT. {'error': {'code': 400, 'message': 'API key expired. Please renew the API key.', 'status': 'INVALID_ARGUMENT', 'details': [{'@type': 'type.googleapis.com/google.rpc.ErrorInfo', 'reason': 'API_KEY_INVALID', 'domain': 'googleapis.com', 'metadata': {'service': 'generativelanguage.googleapis.com'}}, {'@type': 'type.googleapis.com/google.rpc.LocalizedMessage', 'locale': 'en-US', 'message': 'API key expired. Please renew the API key.'}]}}
[2025-09-05 22:24:57] ERROR    API call failed: Error processing image captured_images/capture_20250905_222457.jpg: 400 INVALID_ARGUMENT. {'error': {'code': 400, 'message': 'API key expired. Please renew the API key.', 'status': 'INVALID_ARGUMENT', 'details': [{'@type': 'type.googleapis.com/google.rpc.ErrorInfo', 'reason': 'API_KEY_INVALID', 'domain': 'googleapis.com', 'metadata': {'service': 'generativelanguage.googleapis.com'}}, {'@type': 'type.googleapis.com/google.rpc.LocalizedMessage', 'locale': 'en-US', 'message': 'API key expired. Please renew the API key.'}]}}
[2025-09-05 22:24:57] ERROR    ❌ AI processing failed: Error processing image captured_images/capture_20250905_222457.jpg: 400 INVALID_ARGUMENT. {'error': {'code': 400, 'message': 'API key expired. Please renew the API key.', 'status': 'INVALID_ARGUMENT', 'details': [{'@type': 'type.googleapis.com/google.rpc.ErrorInfo', 'reason': 'API_KEY_INVALID', 'domain': 'googleapis.com', 'metadata': {'service': 'generativelanguage.googleapis.com'}}, {'@type': 'type.googleapis.com/google.rpc.LocalizedMessage', 'locale': 'en-US', 'message': 'API key expired. Please renew the API key.'}]}}
[2025-09-05 22:25:03] INFO     📸 Manual capture initiated from web interface
[2025-09-05 22:25:03] INFO     ✅ Image captured and saved: captured_images/capture_20250905_222503.jpg
[2025-09-05 22:25:03] INFO     Image captured successfully: captured_images/capture_20250905_222503.jpg
[2025-09-05 22:25:03] INFO     📷 Image captured: capture_20250905_222503.jpg
[2025-09-05 22:25:03] INFO     🤖 Processing with AI (License Plate Detection)...
[2025-09-05 22:25:03] INFO     Processing image for License Plate Detection: captured_images/capture_20250905_222503.jpg
[2025-09-05 22:25:04] ERROR    Error processing image captured_images/capture_20250905_222503.jpg: 400 INVALID_ARGUMENT. {'error': {'code': 400, 'message': 'API key expired. Please renew the API key.', 'status': 'INVALID_ARGUMENT', 'details': [{'@type': 'type.googleapis.com/google.rpc.ErrorInfo', 'reason': 'API_KEY_INVALID', 'domain': 'googleapis.com', 'metadata': {'service': 'generativelanguage.googleapis.com'}}, {'@type': 'type.googleapis.com/google.rpc.LocalizedMessage', 'locale': 'en-US', 'message': 'API key expired. Please renew the API key.'}]}}
[2025-09-05 22:25:04] ERROR    API call failed: Error processing image captured_images/capture_20250905_222503.jpg: 400 INVALID_ARGUMENT. {'error': {'code': 400, 'message': 'API key expired. Please renew the API key.', 'status': 'INVALID_ARGUMENT', 'details': [{'@type': 'type.googleapis.com/google.rpc.ErrorInfo', 'reason': 'API_KEY_INVALID', 'domain': 'googleapis.com', 'metadata': {'service': 'generativelanguage.googleapis.com'}}, {'@type': 'type.googleapis.com/google.rpc.LocalizedMessage', 'locale': 'en-US', 'message': 'API key expired. Please renew the API key.'}]}}
[2025-09-05 22:25:04] ERROR    ❌ AI processing failed: Error processing image captured_images/capture_20250905_222503.jpg: 400 INVALID_ARGUMENT. {'error': {'code': 400, 'message': 'API key expired. Please renew the API key.', 'status': 'INVALID_ARGUMENT', 'details': [{'@type': 'type.googleapis.com/google.rpc.ErrorInfo', 'reason': 'API_KEY_INVALID', 'domain': 'googleapis.com', 'metadata': {'service': 'generativelanguage.googleapis.com'}}, {'@type': 'type.googleapis.com/google.rpc.LocalizedMessage', 'locale': 'en-US', 'message': 'API key expired. Please renew the API key.'}]}}
[2025-09-05 22:25:11] INFO     📸 Manual capture initiated from web interface
[2025-09-05 22:25:11] INFO     ✅ Image captured and saved: captured_images/capture_20250905_222511.jpg
[2025-09-05 22:25:11] INFO     Image captured successfully: captured_images/capture_20250905_222511.jpg
[2025-09-05 22:25:11] INFO     📷 Image captured: capture_20250905_222511.jpg
[2025-09-05 22:25:11] INFO     🤖 Processing with AI (License Plate Detection)...
[2025-09-05 22:25:11] INFO     Processing image for License Plate Detection: captured_images/capture_20250905_222511.jpg
[2025-09-05 22:25:11] ERROR    Error processing image captured_images/capture_20250905_222511.jpg: 400 INVALID_ARGUMENT. {'error': {'code': 400, 'message': 'API key expired. Please renew the API key.', 'status': 'INVALID_ARGUMENT', 'details': [{'@type': 'type.googleapis.com/google.rpc.ErrorInfo', 'reason': 'API_KEY_INVALID', 'domain': 'googleapis.com', 'metadata': {'service': 'generativelanguage.googleapis.com'}}, {'@type': 'type.googleapis.com/google.rpc.LocalizedMessage', 'locale': 'en-US', 'message': 'API key expired. Please renew the API key.'}]}}
[2025-09-05 22:25:11] ERROR    API call failed: Error processing image captured_images/capture_20250905_222511.jpg: 400 INVALID_ARGUMENT. {'error': {'code': 400, 'message': 'API key expired. Please renew the API key.', 'status': 'INVALID_ARGUMENT', 'details': [{'@type': 'type.googleapis.com/google.rpc.ErrorInfo', 'reason': 'API_KEY_INVALID', 'domain': 'googleapis.com', 'metadata': {'service': 'generativelanguage.googleapis.com'}}, {'@type': 'type.googleapis.com/google.rpc.LocalizedMessage', 'locale': 'en-US', 'message': 'API key expired. Please renew the API key.'}]}}
[2025-09-05 22:25:11] ERROR    ❌ AI processing failed: Error processing image captured_images/capture_20250905_222511.jpg: 400 INVALID_ARGUMENT. {'error': {'code': 400, 'message': 'API key expired. Please renew the API key.', 'status': 'INVALID_ARGUMENT', 'details': [{'@type': 'type.googleapis.com/google.rpc.ErrorInfo', 'reason': 'API_KEY_INVALID', 'domain': 'googleapis.com', 'metadata': {'service': 'generativelanguage.googleapis.com'}}, {'@type': 'type.googleapis.com/google.rpc.LocalizedMessage', 'locale': 'en-US', 'message': 'API key expired. Please renew the API key.'}]}}
[2025-09-05 22:25:24] INFO     Received signal 2, shutting down gracefully...
[2025-09-05 22:25:24] INFO     🛑 Stopping License Plate Monitoring System...
[2025-09-05 22:25:24] INFO     🔌 Disconnected from RTSP camera
[2025-09-05 22:25:24] INFO     ✅ License Plate Monitoring System stopped
[2025-09-05 22:25:26] INFO     Received signal 2, shutting down gracefully...
[2025-09-05 22:25:26] INFO     🛑 Stopping License Plate Monitoring System...
[2025-09-05 22:25:26] INFO     🔌 Disconnected from RTSP camera
[2025-09-05 22:25:26] INFO     ✅ License Plate Monitoring System stopped
[2025-09-05 22:25:27] INFO     Received signal 2, shutting down gracefully...
[2025-09-05 22:25:27] INFO     🛑 Stopping License Plate Monitoring System...
[2025-09-05 22:25:27] INFO     🔌 Disconnected from RTSP camera
[2025-09-05 22:25:27] INFO     ✅ License Plate Monitoring System stopped
[2025-09-05 22:25:27] INFO     Received signal 2, shutting down gracefully...
[2025-09-05 22:25:27] INFO     🛑 Stopping License Plate Monitoring System...
[2025-09-05 22:25:27] INFO     🔌 Disconnected from RTSP camera
[2025-09-05 22:25:27] INFO     ✅ License Plate Monitoring System stopped
[2025-09-05 22:25:27] INFO     Received signal 2, shutting down gracefully...
[2025-09-05 22:25:27] INFO     🛑 Stopping License Plate Monitoring System...
[2025-09-05 22:25:27] INFO     🔌 Disconnected from RTSP camera
[2025-09-05 22:25:27] INFO     ✅ License Plate Monitoring System stopped
[2025-09-05 22:25:28] INFO     Received signal 2, shutting down gracefully...
[2025-09-05 22:25:28] INFO     🛑 Stopping License Plate Monitoring System...
[2025-09-05 22:25:28] INFO     🔌 Disconnected from RTSP camera
[2025-09-05 22:25:28] INFO     ✅ License Plate Monitoring System stopped
[2025-09-05 22:25:28] INFO     Received signal 2, shutting down gracefully...
[2025-09-05 22:25:28] INFO     🛑 Stopping License Plate Monitoring System...
[2025-09-05 22:25:28] INFO     🔌 Disconnected from RTSP camera
[2025-09-05 22:25:28] INFO     ✅ License Plate Monitoring System stopped
[2025-09-05 22:25:28] INFO     Received signal 2, shutting down gracefully...
[2025-09-05 22:25:28] INFO     🛑 Stopping License Plate Monitoring System...
[2025-09-05 22:25:28] INFO     🔌 Disconnected from RTSP camera
[2025-09-05 22:25:28] INFO     ✅ License Plate Monitoring System stopped
[2025-09-05 22:25:28] INFO     Received signal 2, shutting down gracefully...
[2025-09-05 22:25:28] INFO     🛑 Stopping License Plate Monitoring System...
[2025-09-05 22:25:28] INFO     🔌 Disconnected from RTSP camera
[2025-09-05 22:25:28] INFO     ✅ License Plate Monitoring System stopped
[2025-09-05 22:25:28] INFO     Received signal 2, shutting down gracefully...
[2025-09-05 22:25:28] INFO     🛑 Stopping License Plate Monitoring System...
[2025-09-05 22:25:28] INFO     🔌 Disconnected from RTSP camera
[2025-09-05 22:25:28] INFO     ✅ License Plate Monitoring System stopped
[2025-09-05 22:25:28] INFO     Received signal 2, shutting down gracefully...
[2025-09-05 22:25:28] INFO     🛑 Stopping License Plate Monitoring System...
[2025-09-05 22:25:28] INFO     🔌 Disconnected from RTSP camera
[2025-09-05 22:25:28] INFO     ✅ License Plate Monitoring System stopped
[2025-09-05 22:25:28] INFO     Received signal 2, shutting down gracefully...
[2025-09-05 22:25:28] INFO     🛑 Stopping License Plate Monitoring System...
[2025-09-05 22:25:28] INFO     🔌 Disconnected from RTSP camera
[2025-09-05 22:25:28] INFO     ✅ License Plate Monitoring System stopped
[2025-09-05 22:25:28] INFO     Received signal 2, shutting down gracefully...
[2025-09-05 22:25:28] INFO     🛑 Stopping License Plate Monitoring System...
[2025-09-05 22:25:28] INFO     🔌 Disconnected from RTSP camera
[2025-09-05 22:25:28] INFO     ✅ License Plate Monitoring System stopped
[2025-09-05 22:25:34] INFO     ✅ Configuration loaded from config.json
[2025-09-05 22:25:34] INFO     🌐 Starting License Plate Recognition Web Interface...
[2025-09-05 22:25:35] INFO     🎯 Operating in License Plate Detection mode
[2025-09-05 22:25:35] INFO     📹 Initializing real RTSP camera
[2025-09-05 22:25:35] INFO     Gemini AI client initialized successfully
[2025-09-05 22:25:35] INFO     ✅ AI Vision Monitor initialized for web interface
[2025-09-05 22:25:40] INFO     📸 Manual capture initiated from web interface
[2025-09-05 22:25:40] WARNING  Camera disconnected, attempting to reconnect...
[2025-09-05 22:25:40] INFO     🔌 Connecting to RTSP camera: rtsp://***:***@*************:1935
[2025-09-05 22:25:40] WARNING  ⚠️ Failed to open RTSP stream (attempt 1)
[2025-09-05 22:25:40] INFO     🔄 Retry 2/3 in 2 seconds...
[2025-09-05 22:25:42] INFO     🔌 Connecting to RTSP camera: rtsp://***:***@*************:1935
[2025-09-05 22:25:42] WARNING  ⚠️ Failed to open RTSP stream (attempt 2)
[2025-09-05 22:25:42] INFO     🔄 Retry 3/3 in 4 seconds...
[2025-09-05 22:25:46] INFO     🔌 Connecting to RTSP camera: rtsp://***:***@*************:1935
[2025-09-05 22:25:46] WARNING  ⚠️ Failed to open RTSP stream (attempt 3)
[2025-09-05 22:25:46] ERROR    ❌ Failed to connect to RTSP camera after 3 attempts
[2025-09-05 22:25:46] ERROR    ❌ Failed to reconnect to camera
[2025-09-05 22:25:56] INFO     🤖 Auto mode worker started (interval: 60s)
[2025-09-05 22:25:56] INFO     🔄 Auto mode started with 60s interval
[2025-09-05 22:25:56] WARNING  Camera disconnected, attempting to reconnect...
[2025-09-05 22:25:56] INFO     🔌 Connecting to RTSP camera: rtsp://***:***@*************:1935
[2025-09-05 22:25:56] WARNING  ⚠️ Failed to open RTSP stream (attempt 1)
[2025-09-05 22:25:56] INFO     🔄 Retry 2/3 in 2 seconds...
[2025-09-05 22:25:58] INFO     🔌 Connecting to RTSP camera: rtsp://***:***@*************:1935
[2025-09-05 22:25:58] WARNING  ⚠️ Failed to open RTSP stream (attempt 2)
[2025-09-05 22:25:58] INFO     🔄 Retry 3/3 in 4 seconds...
[2025-09-05 22:26:02] INFO     🔌 Connecting to RTSP camera: rtsp://***:***@*************:1935
[2025-09-05 22:26:02] WARNING  ⚠️ Failed to open RTSP stream (attempt 3)
[2025-09-05 22:26:02] ERROR    ❌ Failed to connect to RTSP camera after 3 attempts
[2025-09-05 22:26:02] ERROR    ❌ Failed to reconnect to camera
[2025-09-05 22:27:03] WARNING  Camera disconnected, attempting to reconnect...
[2025-09-05 22:27:03] INFO     🔌 Connecting to RTSP camera: rtsp://***:***@*************:1935
[2025-09-05 22:27:03] WARNING  ⚠️ Failed to open RTSP stream (attempt 1)
[2025-09-05 22:27:03] INFO     🔄 Retry 2/3 in 2 seconds...
[2025-09-05 22:27:05] INFO     🔌 Connecting to RTSP camera: rtsp://***:***@*************:1935
[2025-09-05 22:27:07] INFO     ✅ Successfully connected to RTSP camera (retry 2, 2 successful reads)
[2025-09-05 22:27:07] INFO     📐 Frame size: (480, 640, 3)
[2025-09-05 22:27:07] INFO     ✅ Image captured and saved: captured_images/capture_20250905_222707.jpg
[2025-09-05 22:27:07] INFO     Image captured successfully: captured_images/capture_20250905_222707.jpg
[2025-09-05 22:27:07] INFO     📷 Image captured: capture_20250905_222707.jpg
[2025-09-05 22:27:07] INFO     🤖 Processing with AI (License Plate Detection)...
[2025-09-05 22:27:07] INFO     Processing image for License Plate Detection: captured_images/capture_20250905_222707.jpg
[2025-09-05 22:27:08] ERROR    Error processing image captured_images/capture_20250905_222707.jpg: 400 INVALID_ARGUMENT. {'error': {'code': 400, 'message': 'API key expired. Please renew the API key.', 'status': 'INVALID_ARGUMENT', 'details': [{'@type': 'type.googleapis.com/google.rpc.ErrorInfo', 'reason': 'API_KEY_INVALID', 'domain': 'googleapis.com', 'metadata': {'service': 'generativelanguage.googleapis.com'}}, {'@type': 'type.googleapis.com/google.rpc.LocalizedMessage', 'locale': 'en-US', 'message': 'API key expired. Please renew the API key.'}]}}
[2025-09-05 22:27:08] ERROR    API call failed: Error processing image captured_images/capture_20250905_222707.jpg: 400 INVALID_ARGUMENT. {'error': {'code': 400, 'message': 'API key expired. Please renew the API key.', 'status': 'INVALID_ARGUMENT', 'details': [{'@type': 'type.googleapis.com/google.rpc.ErrorInfo', 'reason': 'API_KEY_INVALID', 'domain': 'googleapis.com', 'metadata': {'service': 'generativelanguage.googleapis.com'}}, {'@type': 'type.googleapis.com/google.rpc.LocalizedMessage', 'locale': 'en-US', 'message': 'API key expired. Please renew the API key.'}]}}
[2025-09-05 22:27:08] ERROR    ❌ AI processing failed: Error processing image captured_images/capture_20250905_222707.jpg: 400 INVALID_ARGUMENT. {'error': {'code': 400, 'message': 'API key expired. Please renew the API key.', 'status': 'INVALID_ARGUMENT', 'details': [{'@type': 'type.googleapis.com/google.rpc.ErrorInfo', 'reason': 'API_KEY_INVALID', 'domain': 'googleapis.com', 'metadata': {'service': 'generativelanguage.googleapis.com'}}, {'@type': 'type.googleapis.com/google.rpc.LocalizedMessage', 'locale': 'en-US', 'message': 'API key expired. Please renew the API key.'}]}}
[2025-09-05 22:27:42] INFO     Received signal 2, shutting down gracefully...
[2025-09-05 22:27:42] INFO     🛑 Stopping License Plate Monitoring System...
[2025-09-05 22:27:42] INFO     🔌 Disconnected from RTSP camera
[2025-09-05 22:27:42] INFO     ✅ License Plate Monitoring System stopped
[2025-09-05 22:27:46] INFO     Received signal 2, shutting down gracefully...
[2025-09-05 22:27:46] INFO     🛑 Stopping License Plate Monitoring System...
[2025-09-05 22:27:46] INFO     🔌 Disconnected from RTSP camera
[2025-09-05 22:27:46] INFO     ✅ License Plate Monitoring System stopped
[2025-09-05 22:27:47] INFO     Received signal 2, shutting down gracefully...
[2025-09-05 22:27:47] INFO     🛑 Stopping License Plate Monitoring System...
[2025-09-05 22:27:47] INFO     🔌 Disconnected from RTSP camera
[2025-09-05 22:27:47] INFO     ✅ License Plate Monitoring System stopped
[2025-09-05 22:36:17] INFO     ✅ Configuration loaded from config.json
[2025-09-05 22:36:18] INFO     🎯 Operating in License Plate Detection mode
[2025-09-05 22:36:18] INFO     📹 Initializing real RTSP camera
[2025-09-05 22:36:18] INFO     Gemini AI client initialized successfully
[2025-09-05 22:36:18] INFO     ✅ AI Vision Monitor initialized for web interface
[2025-09-05 22:36:18] INFO     🎯 Operating in License Plate Detection mode
[2025-09-05 22:36:18] INFO     📹 Initializing real RTSP camera
[2025-09-05 22:36:18] INFO     Gemini AI client initialized successfully
[2025-09-05 22:36:18] INFO     ✅ AI Vision Monitor initialized for web interface
[2025-09-05 22:36:25] INFO     ✅ Configuration loaded from config.json
[2025-09-05 22:37:53] INFO     ✅ Configuration loaded from config.json
[2025-09-05 22:37:53] INFO     🔄 Mode changed to: object_detection
[2025-09-05 22:37:53] INFO     🎯 Detection mode set to: object_detection
[2025-09-05 22:37:53] INFO     🌐 Starting License Plate Recognition Web Interface...
[2025-09-05 22:37:54] INFO     🎯 Operating in Object Detection mode
[2025-09-05 22:37:54] INFO     📹 Initializing real RTSP camera
[2025-09-05 22:37:54] INFO     Gemini AI client initialized successfully
[2025-09-05 22:37:54] INFO     ✅ AI Vision Monitor initialized for web interface
[2025-09-05 22:38:03] INFO     📸 Manual capture initiated from web interface
[2025-09-05 22:38:03] WARNING  Camera disconnected, attempting to reconnect...
[2025-09-05 22:38:03] INFO     🔌 Connecting to RTSP camera: rtsp://***:***@*************:1935
[2025-09-05 22:38:07] INFO     ✅ Successfully connected to RTSP camera (retry 1, 2 successful reads)
[2025-09-05 22:38:07] INFO     📐 Frame size: (480, 640, 3)
[2025-09-05 22:38:07] INFO     ✅ Image captured and saved: captured_images/capture_20250905_223807.jpg
[2025-09-05 22:38:07] INFO     Image captured successfully: captured_images/capture_20250905_223807.jpg
[2025-09-05 22:38:07] INFO     📷 Image captured: capture_20250905_223807.jpg
[2025-09-05 22:38:07] INFO     🤖 Processing with AI (Object Detection)...
[2025-09-05 22:38:07] INFO     Processing image for Object Detection: captured_images/capture_20250905_223807.jpg
[2025-09-05 22:38:07] ERROR    Error processing image captured_images/capture_20250905_223807.jpg: 400 INVALID_ARGUMENT. {'error': {'code': 400, 'message': 'API key expired. Please renew the API key.', 'status': 'INVALID_ARGUMENT', 'details': [{'@type': 'type.googleapis.com/google.rpc.ErrorInfo', 'reason': 'API_KEY_INVALID', 'domain': 'googleapis.com', 'metadata': {'service': 'generativelanguage.googleapis.com'}}, {'@type': 'type.googleapis.com/google.rpc.LocalizedMessage', 'locale': 'en-US', 'message': 'API key expired. Please renew the API key.'}]}}
[2025-09-05 22:38:07] ERROR    API call failed: Error processing image captured_images/capture_20250905_223807.jpg: 400 INVALID_ARGUMENT. {'error': {'code': 400, 'message': 'API key expired. Please renew the API key.', 'status': 'INVALID_ARGUMENT', 'details': [{'@type': 'type.googleapis.com/google.rpc.ErrorInfo', 'reason': 'API_KEY_INVALID', 'domain': 'googleapis.com', 'metadata': {'service': 'generativelanguage.googleapis.com'}}, {'@type': 'type.googleapis.com/google.rpc.LocalizedMessage', 'locale': 'en-US', 'message': 'API key expired. Please renew the API key.'}]}}
[2025-09-05 22:38:07] ERROR    ❌ AI processing failed: Error processing image captured_images/capture_20250905_223807.jpg: 400 INVALID_ARGUMENT. {'error': {'code': 400, 'message': 'API key expired. Please renew the API key.', 'status': 'INVALID_ARGUMENT', 'details': [{'@type': 'type.googleapis.com/google.rpc.ErrorInfo', 'reason': 'API_KEY_INVALID', 'domain': 'googleapis.com', 'metadata': {'service': 'generativelanguage.googleapis.com'}}, {'@type': 'type.googleapis.com/google.rpc.LocalizedMessage', 'locale': 'en-US', 'message': 'API key expired. Please renew the API key.'}]}}
[2025-09-05 22:38:07] ERROR    ❌ Capture failed: AI processing failed: Error processing image captured_images/capture_20250905_223807.jpg: 400 INVALID_ARGUMENT. {'error': {'code': 400, 'message': 'API key expired. Please renew the API key.', 'status': 'INVALID_ARGUMENT', 'details': [{'@type': 'type.googleapis.com/google.rpc.ErrorInfo', 'reason': 'API_KEY_INVALID', 'domain': 'googleapis.com', 'metadata': {'service': 'generativelanguage.googleapis.com'}}, {'@type': 'type.googleapis.com/google.rpc.LocalizedMessage', 'locale': 'en-US', 'message': 'API key expired. Please renew the API key.'}]}}
[2025-09-05 22:46:54] INFO     Received signal 2, shutting down gracefully...
[2025-09-05 22:46:54] INFO     🛑 Stopping License Plate Monitoring System...
[2025-09-05 22:46:54] INFO     🔌 Disconnected from RTSP camera
[2025-09-05 22:46:54] INFO     ✅ License Plate Monitoring System stopped
[2025-09-05 22:46:55] INFO     Received signal 2, shutting down gracefully...
[2025-09-05 22:46:55] INFO     🛑 Stopping License Plate Monitoring System...
[2025-09-05 22:46:55] INFO     🔌 Disconnected from RTSP camera
[2025-09-05 22:46:55] INFO     ✅ License Plate Monitoring System stopped
[2025-09-05 22:46:55] INFO     Received signal 2, shutting down gracefully...
[2025-09-05 22:46:55] INFO     🛑 Stopping License Plate Monitoring System...
[2025-09-05 22:46:55] INFO     🔌 Disconnected from RTSP camera
[2025-09-05 22:46:55] INFO     ✅ License Plate Monitoring System stopped
[2025-09-05 22:46:55] INFO     Received signal 2, shutting down gracefully...
[2025-09-05 22:46:55] INFO     🛑 Stopping License Plate Monitoring System...
[2025-09-05 22:46:55] INFO     🔌 Disconnected from RTSP camera
[2025-09-05 22:46:55] INFO     ✅ License Plate Monitoring System stopped
[2025-09-05 22:47:02] INFO     ✅ Configuration loaded from config.json
[2025-09-05 22:47:02] INFO     🔄 Mode changed to: object_detection
[2025-09-05 22:47:02] INFO     🎯 Detection mode set to: object_detection
[2025-09-05 22:47:02] INFO     🌐 Starting License Plate Recognition Web Interface...
[2025-09-05 22:47:03] INFO     🎯 Operating in Object Detection mode
[2025-09-05 22:47:03] INFO     📹 Initializing real RTSP camera
[2025-09-05 22:47:03] INFO     🔑 Using API key from config.json: AIzaSyAB...dfXg
[2025-09-05 22:47:03] INFO     ✅ Gemini AI client initialized successfully
[2025-09-05 22:47:03] INFO     ✅ AI Vision Monitor initialized for web interface
[2025-09-05 22:47:16] INFO     📸 Manual capture initiated from web interface
[2025-09-05 22:47:16] WARNING  Camera disconnected, attempting to reconnect...
[2025-09-05 22:47:16] INFO     🔌 Connecting to RTSP camera: rtsp://***:***@*************:1935
[2025-09-05 22:47:19] INFO     ✅ Successfully connected to RTSP camera (retry 1, 2 successful reads)
[2025-09-05 22:47:19] INFO     📐 Frame size: (480, 640, 3)
[2025-09-05 22:47:19] INFO     ✅ Image captured and saved: captured_images/capture_20250905_224719.jpg
[2025-09-05 22:47:19] INFO     Image captured successfully: captured_images/capture_20250905_224719.jpg
[2025-09-05 22:47:19] INFO     📷 Image captured: capture_20250905_224719.jpg
[2025-09-05 22:47:19] INFO     🤖 Processing with AI (Object Detection)...
[2025-09-05 22:47:19] INFO     Processing image for Object Detection: captured_images/capture_20250905_224719.jpg
[2025-09-05 22:47:19] ERROR    ❌ Error processing image captured_images/capture_20250905_224719.jpg: 400 INVALID_ARGUMENT. {'error': {'code': 400, 'message': 'API key expired. Please renew the API key.', 'status': 'INVALID_ARGUMENT', 'details': [{'@type': 'type.googleapis.com/google.rpc.ErrorInfo', 'reason': 'API_KEY_INVALID', 'domain': 'googleapis.com', 'metadata': {'service': 'generativelanguage.googleapis.com'}}, {'@type': 'type.googleapis.com/google.rpc.LocalizedMessage', 'locale': 'en-US', 'message': 'API key expired. Please renew the API key.'}]}}
[2025-09-05 22:47:19] ERROR    API call failed: Error processing image captured_images/capture_20250905_224719.jpg: 400 INVALID_ARGUMENT. {'error': {'code': 400, 'message': 'API key expired. Please renew the API key.', 'status': 'INVALID_ARGUMENT', 'details': [{'@type': 'type.googleapis.com/google.rpc.ErrorInfo', 'reason': 'API_KEY_INVALID', 'domain': 'googleapis.com', 'metadata': {'service': 'generativelanguage.googleapis.com'}}, {'@type': 'type.googleapis.com/google.rpc.LocalizedMessage', 'locale': 'en-US', 'message': 'API key expired. Please renew the API key.'}]}}
[2025-09-05 22:47:19] ERROR    ❌ AI processing failed: Error processing image captured_images/capture_20250905_224719.jpg: 400 INVALID_ARGUMENT. {'error': {'code': 400, 'message': 'API key expired. Please renew the API key.', 'status': 'INVALID_ARGUMENT', 'details': [{'@type': 'type.googleapis.com/google.rpc.ErrorInfo', 'reason': 'API_KEY_INVALID', 'domain': 'googleapis.com', 'metadata': {'service': 'generativelanguage.googleapis.com'}}, {'@type': 'type.googleapis.com/google.rpc.LocalizedMessage', 'locale': 'en-US', 'message': 'API key expired. Please renew the API key.'}]}}
[2025-09-05 22:47:19] ERROR    ❌ Capture failed: AI processing failed: Error processing image captured_images/capture_20250905_224719.jpg: 400 INVALID_ARGUMENT. {'error': {'code': 400, 'message': 'API key expired. Please renew the API key.', 'status': 'INVALID_ARGUMENT', 'details': [{'@type': 'type.googleapis.com/google.rpc.ErrorInfo', 'reason': 'API_KEY_INVALID', 'domain': 'googleapis.com', 'metadata': {'service': 'generativelanguage.googleapis.com'}}, {'@type': 'type.googleapis.com/google.rpc.LocalizedMessage', 'locale': 'en-US', 'message': 'API key expired. Please renew the API key.'}]}}
[2025-09-05 22:48:09] INFO     ✅ Configuration loaded from config.json
[2025-09-05 22:48:10] INFO     🔑 Using API key from config.json: AIzaSyAB...dfXg
[2025-09-05 22:48:10] INFO     ✅ Gemini AI client initialized successfully
[2025-09-05 22:48:10] INFO     🧪 Testing API connection with model: gemini-2.0-flash-001
[2025-09-05 22:48:10] INFO     📸 Manual capture initiated from web interface
[2025-09-05 22:48:10] INFO     ✅ Image captured and saved: captured_images/capture_20250905_224810.jpg
[2025-09-05 22:48:10] INFO     Image captured successfully: captured_images/capture_20250905_224810.jpg
[2025-09-05 22:48:10] INFO     📷 Image captured: capture_20250905_224810.jpg
[2025-09-05 22:48:10] INFO     🤖 Processing with AI (Object Detection)...
[2025-09-05 22:48:10] INFO     Processing image for Object Detection: captured_images/capture_20250905_224810.jpg
[2025-09-05 22:48:10] ERROR    ❌ Error processing image captured_images/capture_20250905_224810.jpg: 400 INVALID_ARGUMENT. {'error': {'code': 400, 'message': 'API key expired. Please renew the API key.', 'status': 'INVALID_ARGUMENT', 'details': [{'@type': 'type.googleapis.com/google.rpc.ErrorInfo', 'reason': 'API_KEY_INVALID', 'domain': 'googleapis.com', 'metadata': {'service': 'generativelanguage.googleapis.com'}}, {'@type': 'type.googleapis.com/google.rpc.LocalizedMessage', 'locale': 'en-US', 'message': 'API key expired. Please renew the API key.'}]}}
[2025-09-05 22:48:10] ERROR    API call failed: Error processing image captured_images/capture_20250905_224810.jpg: 400 INVALID_ARGUMENT. {'error': {'code': 400, 'message': 'API key expired. Please renew the API key.', 'status': 'INVALID_ARGUMENT', 'details': [{'@type': 'type.googleapis.com/google.rpc.ErrorInfo', 'reason': 'API_KEY_INVALID', 'domain': 'googleapis.com', 'metadata': {'service': 'generativelanguage.googleapis.com'}}, {'@type': 'type.googleapis.com/google.rpc.LocalizedMessage', 'locale': 'en-US', 'message': 'API key expired. Please renew the API key.'}]}}
[2025-09-05 22:48:10] ERROR    ❌ AI processing failed: Error processing image captured_images/capture_20250905_224810.jpg: 400 INVALID_ARGUMENT. {'error': {'code': 400, 'message': 'API key expired. Please renew the API key.', 'status': 'INVALID_ARGUMENT', 'details': [{'@type': 'type.googleapis.com/google.rpc.ErrorInfo', 'reason': 'API_KEY_INVALID', 'domain': 'googleapis.com', 'metadata': {'service': 'generativelanguage.googleapis.com'}}, {'@type': 'type.googleapis.com/google.rpc.LocalizedMessage', 'locale': 'en-US', 'message': 'API key expired. Please renew the API key.'}]}}
[2025-09-05 22:48:10] ERROR    ❌ Capture failed: AI processing failed: Error processing image captured_images/capture_20250905_224810.jpg: 400 INVALID_ARGUMENT. {'error': {'code': 400, 'message': 'API key expired. Please renew the API key.', 'status': 'INVALID_ARGUMENT', 'details': [{'@type': 'type.googleapis.com/google.rpc.ErrorInfo', 'reason': 'API_KEY_INVALID', 'domain': 'googleapis.com', 'metadata': {'service': 'generativelanguage.googleapis.com'}}, {'@type': 'type.googleapis.com/google.rpc.LocalizedMessage', 'locale': 'en-US', 'message': 'API key expired. Please renew the API key.'}]}}
[2025-09-05 22:48:11] ERROR    ❌ Gemini API connection test failed: 400 INVALID_ARGUMENT. {'error': {'code': 400, 'message': 'API key expired. Please renew the API key.', 'status': 'INVALID_ARGUMENT', 'details': [{'@type': 'type.googleapis.com/google.rpc.ErrorInfo', 'reason': 'API_KEY_INVALID', 'domain': 'googleapis.com', 'metadata': {'service': 'generativelanguage.googleapis.com'}}, {'@type': 'type.googleapis.com/google.rpc.LocalizedMessage', 'locale': 'en-US', 'message': 'API key expired. Please renew the API key.'}]}}
[2025-09-05 22:48:49] INFO     🔑 Using API key from config.json (ignoring environment variable)
[2025-09-05 22:48:49] INFO     ✅ Configuration loaded from config.json
[2025-09-05 22:48:50] INFO     🔑 Using API key from config.json: AIzaSyCv...BZTI
[2025-09-05 22:48:50] INFO     ✅ Gemini AI client initialized successfully
[2025-09-05 22:48:50] INFO     🧪 Testing API connection with model: gemini-2.0-flash-001
[2025-09-05 22:48:51] INFO     ✅ Gemini API connection test successful. Response: API connection successful
...
[2025-09-05 22:48:53] INFO     Received signal 2, shutting down gracefully...
[2025-09-05 22:48:53] INFO     🛑 Stopping License Plate Monitoring System...
[2025-09-05 22:48:53] INFO     🔌 Disconnected from RTSP camera
[2025-09-05 22:48:53] INFO     ✅ License Plate Monitoring System stopped
[2025-09-05 22:49:06] INFO     🔑 Using API key from config.json (ignoring environment variable)
[2025-09-05 22:49:06] INFO     ✅ Configuration loaded from config.json
[2025-09-05 22:49:06] INFO     🔄 Mode changed to: object_detection
[2025-09-05 22:49:06] INFO     🎯 Detection mode set to: object_detection
[2025-09-05 22:49:06] INFO     🌐 Starting License Plate Recognition Web Interface...
[2025-09-05 22:49:07] INFO     🎯 Operating in Object Detection mode
[2025-09-05 22:49:07] INFO     📹 Initializing real RTSP camera
[2025-09-05 22:49:07] INFO     🔑 Using API key from config.json: AIzaSyDQ...yfuk
[2025-09-05 22:49:07] INFO     ✅ Gemini AI client initialized successfully
[2025-09-05 22:49:07] INFO     ✅ AI Vision Monitor initialized for web interface
[2025-09-05 22:49:10] INFO     📸 Manual capture initiated from web interface
[2025-09-05 22:49:10] WARNING  Camera disconnected, attempting to reconnect...
[2025-09-05 22:49:10] INFO     🔌 Connecting to RTSP camera: rtsp://***:***@*************:1935
[2025-09-05 22:49:14] INFO     ✅ Successfully connected to RTSP camera (retry 1, 2 successful reads)
[2025-09-05 22:49:14] INFO     📐 Frame size: (480, 640, 3)
[2025-09-05 22:49:14] INFO     ✅ Image captured and saved: captured_images/capture_20250905_224914.jpg
[2025-09-05 22:49:14] INFO     Image captured successfully: captured_images/capture_20250905_224914.jpg
[2025-09-05 22:49:14] INFO     📷 Image captured: capture_20250905_224914.jpg
[2025-09-05 22:49:14] INFO     🤖 Processing with AI (Object Detection)...
[2025-09-05 22:49:14] INFO     Processing image for Object Detection: captured_images/capture_20250905_224914.jpg
[2025-09-05 22:49:16] INFO     API call successful: {'success': True, 'mode': 'object_detection', 'description': 'The image is completely black and shows no identifiable objects or features.', 'word_count': 12, 'raw_response': 'The image is completely black and shows no identifiable objects or features.\n', 'image_path': 'captured_images/capture_20250905_224914.jpg'}
[2025-09-05 22:49:16] INFO     👁️  Scene: The image is completely black and shows no identifiable objects or features. (12 words)
[2025-09-05 22:49:16] INFO     SCENE: The image is completely black and shows no identifiable objects or features. | 12 words | capture_20250905_224914.jpg
[2025-09-05 22:49:16] INFO     🔑 Using API key from config.json (ignoring environment variable)
[2025-09-05 22:49:16] INFO     ✅ Configuration loaded from config.json
[2025-09-05 22:49:16] INFO     🔄 Mode changed to: object_detection
[2025-09-05 22:49:16] INFO     🎯 Detection mode set to: object_detection
[2025-09-05 22:49:16] INFO     🌐 Starting License Plate Recognition Web Interface...
[2025-09-05 22:49:17] INFO     🎯 Operating in Object Detection mode
[2025-09-05 22:49:17] INFO     📹 Initializing real RTSP camera
[2025-09-05 22:49:17] INFO     🔑 Using API key from config.json: AIzaSyDQ...yfuk
[2025-09-05 22:49:17] INFO     ✅ Gemini AI client initialized successfully
[2025-09-05 22:49:17] INFO     ✅ AI Vision Monitor initialized for web interface
[2025-09-05 22:49:31] INFO     🤖 Auto mode worker started (interval: 60s)
[2025-09-05 22:49:31] INFO     🔄 Auto mode started with 60s interval
[2025-09-05 22:49:31] INFO     ✅ Image captured and saved: captured_images/capture_20250905_224931.jpg
[2025-09-05 22:49:31] INFO     Image captured successfully: captured_images/capture_20250905_224931.jpg
[2025-09-05 22:49:31] INFO     📷 Image captured: capture_20250905_224931.jpg
[2025-09-05 22:49:31] INFO     🤖 Processing with AI (Object Detection)...
[2025-09-05 22:49:31] INFO     Processing image for Object Detection: captured_images/capture_20250905_224931.jpg
[2025-09-05 22:49:33] INFO     API call successful: {'success': True, 'mode': 'object_detection', 'description': 'The image is completely black; no objects or scene elements are discernible.', 'word_count': 12, 'raw_response': 'The image is completely black; no objects or scene elements are discernible.\n', 'image_path': 'captured_images/capture_20250905_224931.jpg'}
[2025-09-05 22:49:33] INFO     👁️  Scene: The image is completely black; no objects or scene elements are discernible. (12 words)
[2025-09-05 22:49:33] INFO     SCENE: The image is completely black; no objects or scene elements are discernible. | 12 words | capture_20250905_224931.jpg
[2025-09-05 22:49:55] INFO     🔄 Auto mode worker stopped
[2025-09-05 22:49:55] INFO     🔄 Auto mode stopped, switched to web mode
[2025-09-05 22:50:14] INFO     📸 Manual capture initiated from web interface
[2025-09-05 22:50:14] INFO     ✅ Image captured and saved: captured_images/capture_20250905_225014.jpg
[2025-09-05 22:50:14] INFO     Image captured successfully: captured_images/capture_20250905_225014.jpg
[2025-09-05 22:50:14] INFO     📷 Image captured: capture_20250905_225014.jpg
[2025-09-05 22:50:14] INFO     🤖 Processing with AI (Object Detection)...
[2025-09-05 22:50:14] INFO     Processing image for Object Detection: captured_images/capture_20250905_225014.jpg
[2025-09-05 22:50:16] INFO     API call successful: {'success': True, 'mode': 'object_detection', 'description': 'The image is completely black, and nothing is visible.', 'word_count': 9, 'raw_response': 'The image is completely black, and nothing is visible.\n', 'image_path': 'captured_images/capture_20250905_225014.jpg'}
[2025-09-05 22:50:16] INFO     👁️  Scene: The image is completely black, and nothing is visible. (9 words)
[2025-09-05 22:50:16] INFO     SCENE: The image is completely black, and nothing is visible. | 9 words | capture_20250905_225014.jpg
[2025-09-05 22:50:23] INFO     📸 Manual capture initiated from web interface
[2025-09-05 22:50:23] INFO     ✅ Image captured and saved: captured_images/capture_20250905_225023.jpg
[2025-09-05 22:50:23] INFO     Image captured successfully: captured_images/capture_20250905_225023.jpg
[2025-09-05 22:50:23] INFO     📷 Image captured: capture_20250905_225023.jpg
[2025-09-05 22:50:23] INFO     🤖 Processing with AI (Object Detection)...
[2025-09-05 22:50:23] INFO     Processing image for Object Detection: captured_images/capture_20250905_225023.jpg
[2025-09-05 22:50:25] INFO     API call successful: {'success': True, 'mode': 'object_detection', 'description': 'The image is completely dark; no objects or scene elements are discernible.', 'word_count': 12, 'raw_response': 'The image is completely dark; no objects or scene elements are discernible.\n', 'image_path': 'captured_images/capture_20250905_225023.jpg'}
[2025-09-05 22:50:25] INFO     👁️  Scene: The image is completely dark; no objects or scene elements are discernible. (12 words)
[2025-09-05 22:50:25] INFO     SCENE: The image is completely dark; no objects or scene elements are discernible. | 12 words | capture_20250905_225023.jpg
[2025-09-05 22:50:47] INFO     📸 Manual capture initiated from web interface
[2025-09-05 22:50:47] INFO     ✅ Image captured and saved: captured_images/capture_20250905_225047.jpg
[2025-09-05 22:50:47] INFO     Image captured successfully: captured_images/capture_20250905_225047.jpg
[2025-09-05 22:50:47] INFO     📷 Image captured: capture_20250905_225047.jpg
[2025-09-05 22:50:47] INFO     🤖 Processing with AI (Object Detection)...
[2025-09-05 22:50:47] INFO     Processing image for Object Detection: captured_images/capture_20250905_225047.jpg
[2025-09-05 22:50:49] INFO     API call successful: {'success': True, 'mode': 'object_detection', 'description': 'The image appears to be completely dark, showing no discernible objects or features.', 'word_count': 13, 'raw_response': 'The image appears to be completely dark, showing no discernible objects or features.\n', 'image_path': 'captured_images/capture_20250905_225047.jpg'}
[2025-09-05 22:50:49] INFO     👁️  Scene: The image appears to be completely dark, showing no discernible objects or features. (13 words)
[2025-09-05 22:50:49] INFO     SCENE: The image appears to be completely dark, showing no discernible objects or features. | 13 words | capture_20250905_225047.jpg
[2025-09-05 22:51:06] INFO     📸 Manual capture initiated from web interface
[2025-09-05 22:51:06] INFO     ✅ Image captured and saved: captured_images/capture_20250905_225106.jpg
[2025-09-05 22:51:06] INFO     Image captured successfully: captured_images/capture_20250905_225106.jpg
[2025-09-05 22:51:06] INFO     📷 Image captured: capture_20250905_225106.jpg
[2025-09-05 22:51:06] INFO     🤖 Processing with AI (Object Detection)...
[2025-09-05 22:51:06] INFO     Processing image for Object Detection: captured_images/capture_20250905_225106.jpg
[2025-09-05 22:51:08] INFO     API call successful: {'success': True, 'mode': 'object_detection', 'description': 'The image appears to be completely dark, with no discernible objects or features visible.', 'word_count': 14, 'raw_response': 'The image appears to be completely dark, with no discernible objects or features visible.\n', 'image_path': 'captured_images/capture_20250905_225106.jpg'}
[2025-09-05 22:51:08] INFO     👁️  Scene: The image appears to be completely dark, with no discernible objects or features visible. (14 words)
[2025-09-05 22:51:08] INFO     SCENE: The image appears to be completely dark, with no discernible objects or features visible. | 14 words | capture_20250905_225106.jpg
[2025-09-05 22:51:16] INFO     📸 Manual capture initiated from web interface
[2025-09-05 22:51:16] INFO     ✅ Image captured and saved: captured_images/capture_20250905_225116.jpg
[2025-09-05 22:51:16] INFO     Image captured successfully: captured_images/capture_20250905_225116.jpg
[2025-09-05 22:51:16] INFO     📷 Image captured: capture_20250905_225116.jpg
[2025-09-05 22:51:16] INFO     🤖 Processing with AI (Object Detection)...
[2025-09-05 22:51:16] INFO     Processing image for Object Detection: captured_images/capture_20250905_225116.jpg
[2025-09-05 22:51:18] INFO     API call successful: {'success': True, 'mode': 'object_detection', 'description': 'The image is completely dark, showing no discernable objects or features.', 'word_count': 11, 'raw_response': 'The image is completely dark, showing no discernable objects or features.\n', 'image_path': 'captured_images/capture_20250905_225116.jpg'}
[2025-09-05 22:51:18] INFO     👁️  Scene: The image is completely dark, showing no discernable objects or features. (11 words)
[2025-09-05 22:51:18] INFO     SCENE: The image is completely dark, showing no discernable objects or features. | 11 words | capture_20250905_225116.jpg
[2025-09-05 22:51:27] INFO     📸 Manual capture initiated from web interface
[2025-09-05 22:51:27] INFO     ✅ Image captured and saved: captured_images/capture_20250905_225127.jpg
[2025-09-05 22:51:27] INFO     Image captured successfully: captured_images/capture_20250905_225127.jpg
[2025-09-05 22:51:27] INFO     📷 Image captured: capture_20250905_225127.jpg
[2025-09-05 22:51:27] INFO     🤖 Processing with AI (Object Detection)...
[2025-09-05 22:51:27] INFO     Processing image for Object Detection: captured_images/capture_20250905_225127.jpg
[2025-09-05 22:51:29] INFO     API call successful: {'success': True, 'mode': 'object_detection', 'description': 'The image appears to be completely dark; there is nothing visible to analyze.', 'word_count': 13, 'raw_response': 'The image appears to be completely dark; there is nothing visible to analyze.\n', 'image_path': 'captured_images/capture_20250905_225127.jpg'}
[2025-09-05 22:51:29] INFO     👁️  Scene: The image appears to be completely dark; there is nothing visible to analyze. (13 words)
[2025-09-05 22:51:29] INFO     SCENE: The image appears to be completely dark; there is nothing visible to analyze. | 13 words | capture_20250905_225127.jpg
[2025-09-05 22:51:58] INFO     🔄 Mode changed to: license_plate
[2025-09-05 22:51:58] INFO     🔄 Detection mode changed to: license_plate
[2025-09-05 22:51:59] INFO     📸 Manual capture initiated from web interface
[2025-09-05 22:51:59] INFO     ✅ Image captured and saved: captured_images/capture_20250905_225159.jpg
[2025-09-05 22:51:59] INFO     Image captured successfully: captured_images/capture_20250905_225159.jpg
[2025-09-05 22:51:59] INFO     📷 Image captured: capture_20250905_225159.jpg
[2025-09-05 22:51:59] INFO     🤖 Processing with AI (License Plate Detection)...
[2025-09-05 22:51:59] INFO     Processing image for License Plate Detection: captured_images/capture_20250905_225159.jpg
[2025-09-05 22:52:00] INFO     API call successful: {'success': True, 'license_plates': [], 'plates_detected': 0, 'raw_response': 'LICENSE PLATES DETECTED: 0\nNo license plates visible in this image.', 'image_path': 'captured_images/capture_20250905_225159.jpg'}
[2025-09-05 22:52:00] INFO     No license plates detected in captured_images/capture_20250905_225159.jpg
[2025-09-05 22:52:00] INFO     ℹ️  No license plates detected
[2025-09-05 22:52:12] INFO     🤖 Auto mode worker started (interval: 5s)
[2025-09-05 22:52:12] INFO     🔄 Auto mode started with 5s interval
[2025-09-05 22:52:12] INFO     ✅ Image captured and saved: captured_images/capture_20250905_225212.jpg
[2025-09-05 22:52:12] INFO     Image captured successfully: captured_images/capture_20250905_225212.jpg
[2025-09-05 22:52:12] INFO     📷 Image captured: capture_20250905_225212.jpg
[2025-09-05 22:52:12] INFO     🤖 Processing with AI (License Plate Detection)...
[2025-09-05 22:52:12] INFO     Processing image for License Plate Detection: captured_images/capture_20250905_225212.jpg
[2025-09-05 22:52:14] INFO     API call successful: {'success': True, 'license_plates': [], 'plates_detected': 0, 'raw_response': 'LICENSE PLATES DETECTED: 0\nNo license plates visible in this image.', 'image_path': 'captured_images/capture_20250905_225212.jpg'}
[2025-09-05 22:52:14] INFO     No license plates detected in captured_images/capture_20250905_225212.jpg
[2025-09-05 22:52:14] INFO     ℹ️  No license plates detected
[2025-09-05 22:52:19] INFO     ✅ Image captured and saved: captured_images/capture_20250905_225219.jpg
[2025-09-05 22:52:19] INFO     Image captured successfully: captured_images/capture_20250905_225219.jpg
[2025-09-05 22:52:19] INFO     📷 Image captured: capture_20250905_225219.jpg
[2025-09-05 22:52:19] INFO     🤖 Processing with AI (License Plate Detection)...
[2025-09-05 22:52:19] INFO     Processing image for License Plate Detection: captured_images/capture_20250905_225219.jpg
[2025-09-05 22:52:21] INFO     API call successful: {'success': True, 'license_plates': [], 'plates_detected': 0, 'raw_response': 'LICENSE PLATES DETECTED: 0\nNo license plates visible in this image.', 'image_path': 'captured_images/capture_20250905_225219.jpg'}
[2025-09-05 22:52:21] INFO     No license plates detected in captured_images/capture_20250905_225219.jpg
[2025-09-05 22:52:21] INFO     ℹ️  No license plates detected
[2025-09-05 22:52:24] INFO     🔄 Auto mode worker stopped
[2025-09-05 22:52:24] INFO     🔄 Auto mode stopped, switched to web mode
[2025-09-05 22:52:51] INFO     🔑 Using API key from config.json (ignoring environment variable)
[2025-09-05 22:52:51] INFO     ✅ Configuration loaded from config.json
[2025-09-05 22:52:52] ERROR    ❌ OpenCV not available - cannot use real RTSP camera
[2025-09-05 22:52:52] ERROR    ❌ OpenCV not available - cannot use real RTSP camera
[2025-09-05 22:53:31] INFO     🔑 Using API key from config.json (ignoring environment variable)
[2025-09-05 22:53:31] INFO     ✅ Configuration loaded from config.json
[2025-09-05 22:53:31] INFO     🔌 Connecting to RTSP camera: rtsp://***:***@*************:1935
[2025-09-05 22:53:34] INFO     ✅ Successfully connected to RTSP camera (retry 1, 2 successful reads)
[2025-09-05 22:53:34] INFO     📐 Frame size: (480, 640, 3)
[2025-09-05 22:53:34] INFO     ✅ Image captured and saved: captured_images/capture_20250905_225334.jpg
[2025-09-05 22:53:34] INFO     Image captured successfully: captured_images/capture_20250905_225334.jpg
[2025-09-05 22:53:35] INFO     ✅ Image captured and saved: captured_images/capture_20250905_225335.jpg
[2025-09-05 22:53:35] INFO     Image captured successfully: captured_images/capture_20250905_225335.jpg
[2025-09-05 22:53:36] INFO     ✅ Image captured and saved: captured_images/capture_20250905_225336.jpg
[2025-09-05 22:53:36] INFO     Image captured successfully: captured_images/capture_20250905_225336.jpg
[2025-09-05 22:53:37] INFO     🔌 Disconnected from RTSP camera
[2025-09-05 22:53:37] INFO     🔌 Connecting to RTSP camera: rtsp://***:***@*************:1935
[2025-09-05 22:53:40] INFO     ✅ Successfully connected to RTSP camera (retry 1, 2 successful reads)
[2025-09-05 22:53:40] INFO     📐 Frame size: (480, 640, 3)
[2025-09-05 22:53:40] INFO     ✅ Image captured and saved: captured_images/capture_20250905_225340.jpg
[2025-09-05 22:53:40] INFO     Image captured successfully: captured_images/capture_20250905_225340.jpg
[2025-09-05 22:53:40] INFO     🔌 Disconnected from RTSP camera
[2025-09-05 22:54:17] INFO     Received signal 2, shutting down gracefully...
[2025-09-05 22:54:17] INFO     🛑 Stopping License Plate Monitoring System...
[2025-09-05 22:54:17] INFO     🔌 Disconnected from RTSP camera
[2025-09-05 22:54:17] INFO     ✅ License Plate Monitoring System stopped
[2025-09-05 22:54:38] INFO     🔑 Using API key from config.json (ignoring environment variable)
[2025-09-05 22:54:38] INFO     ✅ Configuration loaded from config.json
[2025-09-05 22:54:38] INFO     🔌 Connecting to RTSP camera: rtsp://***:***@*************:1935
[2025-09-05 22:54:38] WARNING  ⚠️ Failed to open RTSP stream (attempt 1)
[2025-09-05 22:54:38] INFO     🔄 Retry 2/3 in 2 seconds...
[2025-09-05 22:54:39] INFO     🔑 Using API key from config.json (ignoring environment variable)
[2025-09-05 22:54:39] INFO     ✅ Configuration loaded from config.json
[2025-09-05 22:54:39] INFO     🔄 Mode changed to: object_detection
[2025-09-05 22:54:39] INFO     🎯 Detection mode set to: object_detection
[2025-09-05 22:54:39] INFO     🌐 Starting License Plate Recognition Web Interface...
[2025-09-05 22:54:40] INFO     🎯 Operating in Object Detection mode
[2025-09-05 22:54:40] INFO     📹 Initializing real RTSP camera
[2025-09-05 22:54:40] INFO     🔑 Using API key from config.json: AIzaSyDQ...yfuk
[2025-09-05 22:54:40] INFO     ✅ Gemini AI client initialized successfully
[2025-09-05 22:54:40] INFO     ✅ AI Vision Monitor initialized for web interface
[2025-09-05 22:54:40] INFO     🔌 Connecting to RTSP camera: rtsp://***:***@*************:1935
[2025-09-05 22:54:41] WARNING  ⚠️ Failed to open RTSP stream (attempt 2)
[2025-09-05 22:54:41] INFO     🔄 Retry 3/3 in 4 seconds...
[2025-09-05 22:54:44] INFO     📸 Manual capture initiated from web interface
[2025-09-05 22:54:44] WARNING  Camera disconnected, attempting to reconnect...
[2025-09-05 22:54:44] INFO     🔌 Connecting to RTSP camera: rtsp://***:***@*************:1935
[2025-09-05 22:54:44] WARNING  ⚠️ Failed to open RTSP stream (attempt 1)
[2025-09-05 22:54:44] INFO     🔄 Retry 2/3 in 2 seconds...
[2025-09-05 22:54:45] INFO     🔌 Connecting to RTSP camera: rtsp://***:***@*************:1935
[2025-09-05 22:54:45] WARNING  ⚠️ Failed to open RTSP stream (attempt 3)
[2025-09-05 22:54:45] ERROR    ❌ Failed to connect to RTSP camera after 3 attempts
[2025-09-05 22:54:46] INFO     🔌 Connecting to RTSP camera: rtsp://***:***@*************:1935
[2025-09-05 22:54:46] WARNING  ⚠️ Failed to open RTSP stream (attempt 2)
[2025-09-05 22:54:46] INFO     🔄 Retry 3/3 in 4 seconds...
[2025-09-05 22:54:50] INFO     🔌 Connecting to RTSP camera: rtsp://***:***@*************:1935
[2025-09-05 22:54:50] WARNING  ⚠️ Failed to open RTSP stream (attempt 3)
[2025-09-05 22:54:50] ERROR    ❌ Failed to connect to RTSP camera after 3 attempts
[2025-09-05 22:54:50] ERROR    ❌ Failed to reconnect to camera
[2025-09-05 22:54:50] ERROR    ❌ Capture failed: Failed to connect to camera
[2025-09-05 22:54:59] INFO     📸 Manual capture initiated from web interface
[2025-09-05 22:54:59] WARNING  Camera disconnected, attempting to reconnect...
[2025-09-05 22:54:59] INFO     🔌 Connecting to RTSP camera: rtsp://***:***@*************:1935
[2025-09-05 22:54:59] WARNING  ⚠️ Failed to open RTSP stream (attempt 1)
[2025-09-05 22:54:59] INFO     🔄 Retry 2/3 in 2 seconds...
[2025-09-05 22:55:01] INFO     🔌 Connecting to RTSP camera: rtsp://***:***@*************:1935
[2025-09-05 22:55:01] WARNING  ⚠️ Failed to open RTSP stream (attempt 2)
[2025-09-05 22:55:01] INFO     🔄 Retry 3/3 in 4 seconds...
[2025-09-05 22:55:05] INFO     🔌 Connecting to RTSP camera: rtsp://***:***@*************:1935
[2025-09-05 22:55:05] WARNING  ⚠️ Failed to open RTSP stream (attempt 3)
[2025-09-05 22:55:05] ERROR    ❌ Failed to connect to RTSP camera after 3 attempts
[2025-09-05 22:55:05] ERROR    ❌ Failed to reconnect to camera
[2025-09-05 22:55:05] ERROR    ❌ Capture failed: Failed to connect to camera
[2025-09-05 22:55:20] INFO     🔑 Using API key from config.json (ignoring environment variable)
[2025-09-05 22:55:20] INFO     ✅ Configuration loaded from config.json
[2025-09-05 22:55:22] INFO     🎯 Operating in License Plate Detection mode
[2025-09-05 22:55:22] INFO     📹 Initializing real RTSP camera
[2025-09-05 22:55:22] INFO     🔑 Using API key from config.json: AIzaSyDQ...yfuk
[2025-09-05 22:55:22] INFO     ✅ Gemini AI client initialized successfully
[2025-09-05 22:55:22] INFO     🤖 Testing AI processor connection...
[2025-09-05 22:55:22] INFO     🧪 Testing API connection with model: gemini-2.0-flash-001
[2025-09-05 22:55:23] INFO     ✅ Gemini API connection test successful. Response: API connection successful
...
[2025-09-05 22:55:23] INFO     📷 Testing camera connection...
[2025-09-05 22:55:23] INFO     🔄 Attempting to connect to real RTSP camera...
[2025-09-05 22:55:23] INFO     🔌 Connecting to RTSP camera: rtsp://***:***@*************:1935
[2025-09-05 22:55:23] WARNING  ⚠️ Failed to open RTSP stream (attempt 1)
[2025-09-05 22:55:23] INFO     🔄 Retry 2/3 in 2 seconds...
[2025-09-05 22:55:25] INFO     🔌 Connecting to RTSP camera: rtsp://***:***@*************:1935
[2025-09-05 22:55:25] WARNING  ⚠️ Failed to open RTSP stream (attempt 2)
[2025-09-05 22:55:25] INFO     🔄 Retry 3/3 in 4 seconds...
[2025-09-05 22:55:29] INFO     🔌 Connecting to RTSP camera: rtsp://***:***@*************:1935
[2025-09-05 22:55:32] INFO     ✅ Successfully connected to RTSP camera (retry 3, 2 successful reads)
[2025-09-05 22:55:32] INFO     📐 Frame size: (480, 640, 3)
[2025-09-05 22:55:32] INFO     ✅ Real RTSP camera connected successfully!
[2025-09-05 22:55:32] INFO     ⏰ Scheduling capture every 1 minute(s)
[2025-09-05 22:55:32] INFO     📸 Manual capture initiated from web interface
[2025-09-05 22:55:32] WARNING  Camera disconnected, attempting to reconnect...
[2025-09-05 22:55:32] INFO     🔌 Connecting to RTSP camera: rtsp://***:***@*************:1935
[2025-09-05 22:55:36] INFO     ✅ Successfully connected to RTSP camera (retry 1, 2 successful reads)
[2025-09-05 22:55:36] INFO     📐 Frame size: (480, 640, 3)
[2025-09-05 22:55:36] INFO     ✅ Image captured and saved: captured_images/capture_20250905_225536.jpg
[2025-09-05 22:55:36] INFO     Image captured successfully: captured_images/capture_20250905_225536.jpg
[2025-09-05 22:55:36] INFO     📷 Image captured: capture_20250905_225536.jpg
[2025-09-05 22:55:36] INFO     🤖 Processing with AI (Object Detection)...
[2025-09-05 22:55:36] INFO     Processing image for Object Detection: captured_images/capture_20250905_225536.jpg
[2025-09-05 22:55:38] INFO     API call successful: {'success': True, 'mode': 'object_detection', 'description': 'Person lies below a decorative light fixture.', 'word_count': 7, 'raw_response': 'Person lies below a decorative light fixture.', 'image_path': 'captured_images/capture_20250905_225536.jpg'}
[2025-09-05 22:55:38] INFO     👁️  Scene: Person lies below a decorative light fixture. (7 words)
[2025-09-05 22:55:38] INFO     SCENE: Person lies below a decorative light fixture. | 7 words | capture_20250905_225536.jpg
[2025-09-05 22:55:39] INFO     📸 Manual capture initiated from web interface
[2025-09-05 22:55:39] INFO     ✅ Image captured and saved: captured_images/capture_20250905_225539.jpg
[2025-09-05 22:55:39] INFO     Image captured successfully: captured_images/capture_20250905_225539.jpg
[2025-09-05 22:55:39] INFO     📷 Image captured: capture_20250905_225539.jpg
[2025-09-05 22:55:39] INFO     🤖 Processing with AI (Object Detection)...
[2025-09-05 22:55:39] INFO     Processing image for Object Detection: captured_images/capture_20250905_225539.jpg
[2025-09-05 22:55:41] INFO     API call successful: {'success': True, 'mode': 'object_detection', 'description': 'Person lies down looking up at a decorative light on the ceiling.', 'word_count': 12, 'raw_response': 'Person lies down looking up at a decorative light on the ceiling.\n', 'image_path': 'captured_images/capture_20250905_225539.jpg'}
[2025-09-05 22:55:41] INFO     👁️  Scene: Person lies down looking up at a decorative light on the ceiling. (12 words)
[2025-09-05 22:55:41] INFO     SCENE: Person lies down looking up at a decorative light on the ceiling. | 12 words | capture_20250905_225539.jpg
[2025-09-05 22:55:47] INFO     📸 Manual capture initiated from web interface
[2025-09-05 22:55:47] INFO     ✅ Image captured and saved: captured_images/capture_20250905_225547.jpg
[2025-09-05 22:55:47] INFO     Image captured successfully: captured_images/capture_20250905_225547.jpg
[2025-09-05 22:55:47] INFO     📷 Image captured: capture_20250905_225547.jpg
[2025-09-05 22:55:47] INFO     🤖 Processing with AI (Object Detection)...
[2025-09-05 22:55:47] INFO     Processing image for Object Detection: captured_images/capture_20250905_225547.jpg
[2025-09-05 22:55:49] INFO     API call successful: {'success': True, 'mode': 'object_detection', 'description': 'Person looks up at illuminated ceiling with abstract design.', 'word_count': 9, 'raw_response': 'Person looks up at illuminated ceiling with abstract design.\n', 'image_path': 'captured_images/capture_20250905_225547.jpg'}
[2025-09-05 22:55:49] INFO     👁️  Scene: Person looks up at illuminated ceiling with abstract design. (9 words)
[2025-09-05 22:55:49] INFO     SCENE: Person looks up at illuminated ceiling with abstract design. | 9 words | capture_20250905_225547.jpg
[2025-09-05 22:56:06] INFO     Received signal 2, shutting down gracefully...
[2025-09-05 22:56:06] INFO     🛑 Stopping License Plate Monitoring System...
[2025-09-05 22:56:06] INFO     🔌 Disconnected from RTSP camera
[2025-09-05 22:56:06] INFO     ✅ License Plate Monitoring System stopped
[2025-09-05 22:56:16] INFO     🔑 Using API key from config.json (ignoring environment variable)
[2025-09-05 22:56:16] INFO     ✅ Configuration loaded from config.json
[2025-09-05 22:56:17] INFO     🎯 Operating in License Plate Detection mode
[2025-09-05 22:56:17] INFO     📹 Initializing real RTSP camera
[2025-09-05 22:56:17] INFO     🔑 Using API key from config.json: AIzaSyDQ...yfuk
[2025-09-05 22:56:17] INFO     ✅ Gemini AI client initialized successfully
[2025-09-05 22:56:17] INFO     🤖 Testing AI processor connection...
[2025-09-05 22:56:17] INFO     🧪 Testing API connection with model: gemini-2.0-flash-001
[2025-09-05 22:56:18] INFO     ✅ Gemini API connection test successful. Response: API connection successful
...
[2025-09-05 22:56:18] INFO     📷 Testing camera connection...
[2025-09-05 22:56:18] INFO     🔄 Attempting to connect to real RTSP camera...
[2025-09-05 22:56:18] INFO     🔌 Connecting to RTSP camera: rtsp://***:***@*************:1935
[2025-09-05 22:56:21] INFO     ✅ Successfully connected to RTSP camera (retry 1, 2 successful reads)
[2025-09-05 22:56:21] INFO     📐 Frame size: (480, 640, 3)
[2025-09-05 22:56:21] INFO     ✅ Real RTSP camera connected successfully!
[2025-09-05 22:56:21] INFO     ⏰ Scheduling capture every 1 minute(s)
[2025-09-05 22:56:29] INFO     Received signal 2, shutting down gracefully...
[2025-09-05 22:56:29] INFO     🛑 Stopping License Plate Monitoring System...
[2025-09-05 22:56:29] INFO     🔌 Disconnected from RTSP camera
[2025-09-05 22:56:29] INFO     ✅ License Plate Monitoring System stopped
[2025-09-05 22:56:30] INFO     🛑 Stopping License Plate Monitoring System...
[2025-09-05 22:56:30] INFO     🔌 Disconnected from RTSP camera
[2025-09-05 22:56:30] INFO     ✅ License Plate Monitoring System stopped
[2025-09-05 22:56:35] INFO     🔑 Using API key from config.json (ignoring environment variable)
[2025-09-05 22:56:35] INFO     ✅ Configuration loaded from config.json
[2025-09-05 22:56:35] INFO     🔄 Mode changed to: object_detection
[2025-09-05 22:56:35] INFO     🎯 Detection mode set to: object_detection
[2025-09-05 22:56:35] INFO     🌐 Starting License Plate Recognition Web Interface...
[2025-09-05 22:56:37] INFO     🎯 Operating in Object Detection mode
[2025-09-05 22:56:37] INFO     📹 Initializing real RTSP camera
[2025-09-05 22:56:37] INFO     🔑 Using API key from config.json: AIzaSyDQ...yfuk
[2025-09-05 22:56:37] INFO     ✅ Gemini AI client initialized successfully
[2025-09-05 22:56:37] INFO     ✅ AI Vision Monitor initialized for web interface
[2025-09-05 22:56:42] INFO     📸 Manual capture initiated from web interface
[2025-09-05 22:56:42] WARNING  Camera disconnected, attempting to reconnect...
[2025-09-05 22:56:42] INFO     🔌 Connecting to RTSP camera: rtsp://***:***@*************:1935
[2025-09-05 22:56:42] WARNING  ⚠️ Failed to open RTSP stream (attempt 1)
[2025-09-05 22:56:42] INFO     🔄 Retry 2/3 in 2 seconds...
[2025-09-05 22:56:44] INFO     🔌 Connecting to RTSP camera: rtsp://***:***@*************:1935
[2025-09-05 22:56:44] WARNING  ⚠️ Failed to open RTSP stream (attempt 2)
[2025-09-05 22:56:44] INFO     🔄 Retry 3/3 in 4 seconds...
[2025-09-05 22:56:48] INFO     🔌 Connecting to RTSP camera: rtsp://***:***@*************:1935
[2025-09-05 22:56:48] WARNING  ⚠️ Failed to open RTSP stream (attempt 3)
[2025-09-05 22:56:48] ERROR    ❌ Failed to connect to RTSP camera after 3 attempts
[2025-09-05 22:56:48] ERROR    ❌ Failed to reconnect to camera
[2025-09-05 22:56:48] ERROR    ❌ Capture failed: Failed to connect to camera
[2025-09-05 22:56:59] INFO     📸 Manual capture initiated from web interface
[2025-09-05 22:56:59] WARNING  Camera disconnected, attempting to reconnect...
[2025-09-05 22:56:59] INFO     🔌 Connecting to RTSP camera: rtsp://***:***@*************:1935
[2025-09-05 22:57:02] INFO     ✅ Successfully connected to RTSP camera (retry 1, 2 successful reads)
[2025-09-05 22:57:02] INFO     📐 Frame size: (480, 640, 3)
[2025-09-05 22:57:02] INFO     ✅ Image captured and saved: captured_images/capture_20250905_225702.jpg
[2025-09-05 22:57:02] INFO     Image captured successfully: captured_images/capture_20250905_225702.jpg
[2025-09-05 22:57:02] INFO     📷 Image captured: capture_20250905_225702.jpg
[2025-09-05 22:57:02] INFO     🤖 Processing with AI (Object Detection)...
[2025-09-05 22:57:02] INFO     Processing image for Object Detection: captured_images/capture_20250905_225702.jpg
[2025-09-05 22:57:04] INFO     API call successful: {'success': True, 'mode': 'object_detection', 'description': 'Person typing on a laptop keyboard in bed near a wall adapter.', 'word_count': 12, 'raw_response': 'Person typing on a laptop keyboard in bed near a wall adapter.\n', 'image_path': 'captured_images/capture_20250905_225702.jpg'}
[2025-09-05 22:57:04] INFO     👁️  Scene: Person typing on a laptop keyboard in bed near a wall adapter. (12 words)
[2025-09-05 22:57:04] INFO     SCENE: Person typing on a laptop keyboard in bed near a wall adapter. | 12 words | capture_20250905_225702.jpg
[2025-09-05 22:57:13] INFO     📸 Manual capture initiated from web interface
[2025-09-05 22:57:13] INFO     ✅ Image captured and saved: captured_images/capture_20250905_225713.jpg
[2025-09-05 22:57:13] INFO     Image captured successfully: captured_images/capture_20250905_225713.jpg
[2025-09-05 22:57:13] INFO     📷 Image captured: capture_20250905_225713.jpg
[2025-09-05 22:57:13] INFO     🤖 Processing with AI (Object Detection)...
[2025-09-05 22:57:13] INFO     Processing image for Object Detection: captured_images/capture_20250905_225713.jpg
[2025-09-05 22:57:15] INFO     API call successful: {'success': True, 'mode': 'object_detection', 'description': 'Hand types on laptop keyboard near tablet; bedding is also visible.', 'word_count': 11, 'raw_response': 'Hand types on laptop keyboard near tablet; bedding is also visible.\n', 'image_path': 'captured_images/capture_20250905_225713.jpg'}
[2025-09-05 22:57:15] INFO     👁️  Scene: Hand types on laptop keyboard near tablet; bedding is also visible. (11 words)
[2025-09-05 22:57:15] INFO     SCENE: Hand types on laptop keyboard near tablet; bedding is also visible. | 11 words | capture_20250905_225713.jpg
[2025-09-05 22:57:42] INFO     📸 Manual capture initiated from web interface
[2025-09-05 22:57:42] INFO     ✅ Image captured and saved: captured_images/capture_20250905_225742.jpg
[2025-09-05 22:57:42] INFO     Image captured successfully: captured_images/capture_20250905_225742.jpg
[2025-09-05 22:57:42] INFO     📷 Image captured: capture_20250905_225742.jpg
[2025-09-05 22:57:42] INFO     🤖 Processing with AI (Object Detection)...
[2025-09-05 22:57:42] INFO     Processing image for Object Detection: captured_images/capture_20250905_225742.jpg
[2025-09-05 22:57:44] INFO     API call successful: {'success': True, 'mode': 'object_detection', 'description': 'A hand is typing on a laptop keyboard next to a tablet device.', 'word_count': 13, 'raw_response': 'A hand is typing on a laptop keyboard next to a tablet device.\n', 'image_path': 'captured_images/capture_20250905_225742.jpg'}
[2025-09-05 22:57:44] INFO     👁️  Scene: A hand is typing on a laptop keyboard next to a tablet device. (13 words)
[2025-09-05 22:57:44] INFO     SCENE: A hand is typing on a laptop keyboard next to a tablet device. | 13 words | capture_20250905_225742.jpg
[2025-09-05 22:57:57] INFO     📸 Manual capture initiated from web interface
[2025-09-05 22:57:57] INFO     ✅ Image captured and saved: captured_images/capture_20250905_225757.jpg
[2025-09-05 22:57:57] INFO     Image captured successfully: captured_images/capture_20250905_225757.jpg
[2025-09-05 22:57:57] INFO     📷 Image captured: capture_20250905_225757.jpg
[2025-09-05 22:57:57] INFO     🤖 Processing with AI (Object Detection)...
[2025-09-05 22:57:57] INFO     Processing image for Object Detection: captured_images/capture_20250905_225757.jpg
[2025-09-05 22:57:59] INFO     API call successful: {'success': True, 'mode': 'object_detection', 'description': 'Hand on laptop keyboard on bed, working on a screen capture.', 'word_count': 11, 'raw_response': 'Hand on laptop keyboard on bed, working on a screen capture.\n', 'image_path': 'captured_images/capture_20250905_225757.jpg'}
[2025-09-05 22:57:59] INFO     👁️  Scene: Hand on laptop keyboard on bed, working on a screen capture. (11 words)
[2025-09-05 22:57:59] INFO     SCENE: Hand on laptop keyboard on bed, working on a screen capture. | 11 words | capture_20250905_225757.jpg
[2025-09-05 23:00:35] INFO     🔑 Using API key from config.json (ignoring environment variable)
[2025-09-05 23:00:35] INFO     ✅ Configuration loaded from config.json
[2025-09-05 23:00:40] INFO     🎯 Operating in License Plate Detection mode
[2025-09-05 23:00:40] INFO     📹 Initializing real RTSP camera
[2025-09-05 23:00:40] INFO     🔑 Using API key from config.json: AIzaSyDQ...yfuk
[2025-09-05 23:00:40] INFO     ✅ Gemini AI client initialized successfully
[2025-09-05 23:00:40] INFO     ✅ AI Vision Monitor initialized for web interface
[2025-09-05 23:00:58] INFO     🔑 Using API key from config.json (ignoring environment variable)
[2025-09-05 23:00:58] INFO     ✅ Configuration loaded from config.json
[2025-09-05 23:00:59] INFO     🎯 Operating in License Plate Detection mode
[2025-09-05 23:00:59] INFO     📹 Initializing real RTSP camera
[2025-09-05 23:00:59] INFO     🔑 Using API key from config.json: AIzaSyDQ...yfuk
[2025-09-05 23:00:59] INFO     ✅ Gemini AI client initialized successfully
[2025-09-05 23:00:59] INFO     ✅ AI Vision Monitor initialized for web interface
[2025-09-05 23:01:06] INFO     📸 Manual capture initiated from web interface
[2025-09-05 23:01:06] WARNING  Camera disconnected, attempting to reconnect...
[2025-09-05 23:01:06] INFO     🔌 Connecting to RTSP camera: rtsp://***:***@*************:1935
[2025-09-05 23:01:06] WARNING  ⚠️ Failed to open RTSP stream (attempt 1)
[2025-09-05 23:01:06] INFO     🔄 Retry 2/3 in 2 seconds...
[2025-09-05 23:01:08] INFO     🔌 Connecting to RTSP camera: rtsp://***:***@*************:1935
[2025-09-05 23:01:08] WARNING  ⚠️ Failed to open RTSP stream (attempt 2)
[2025-09-05 23:01:08] INFO     🔄 Retry 3/3 in 4 seconds...
[2025-09-05 23:01:12] INFO     🔌 Connecting to RTSP camera: rtsp://***:***@*************:1935
[2025-09-05 23:01:12] WARNING  ⚠️ Failed to open RTSP stream (attempt 3)
[2025-09-05 23:01:12] ERROR    ❌ Failed to connect to RTSP camera after 3 attempts
[2025-09-05 23:01:12] ERROR    ❌ Failed to reconnect to camera
[2025-09-05 23:01:12] ERROR    ❌ Capture failed: Failed to connect to camera
[2025-09-05 23:01:19] INFO     📸 Manual capture initiated from web interface
[2025-09-05 23:01:19] WARNING  Camera disconnected, attempting to reconnect...
[2025-09-05 23:01:19] INFO     🔌 Connecting to RTSP camera: rtsp://***:***@*************:1935
[2025-09-05 23:01:21] INFO     ✅ Successfully connected to RTSP camera (retry 1, 2 successful reads)
[2025-09-05 23:01:21] INFO     📐 Frame size: (480, 640, 3)
[2025-09-05 23:01:21] INFO     ✅ Image captured and saved: captured_images/capture_20250905_230121.jpg
[2025-09-05 23:01:21] INFO     Image captured successfully: captured_images/capture_20250905_230121.jpg
[2025-09-05 23:01:21] INFO     📷 Image captured: capture_20250905_230121.jpg
[2025-09-05 23:01:21] INFO     🤖 Processing with AI (License Plate Detection)...
[2025-09-05 23:01:21] INFO     Processing image for License Plate Detection: captured_images/capture_20250905_230121.jpg
[2025-09-05 23:01:24] INFO     API call successful: {'success': True, 'license_plates': [], 'plates_detected': 0, 'raw_response': 'LICENSE PLATES DETECTED: 0\nNo license plates visible in this image.', 'image_path': 'captured_images/capture_20250905_230121.jpg'}
[2025-09-05 23:01:24] INFO     No license plates detected in captured_images/capture_20250905_230121.jpg
[2025-09-05 23:01:24] INFO     ℹ️  No license plates detected
[2025-09-05 23:02:01] INFO     🔑 Using API key from config.json (ignoring environment variable)
[2025-09-05 23:02:01] INFO     ✅ Configuration loaded from config.json
[2025-09-05 23:02:01] INFO     🎯 Operating in License Plate Detection mode
[2025-09-05 23:02:01] INFO     📹 Initializing real RTSP camera
[2025-09-05 23:02:01] INFO     🔑 Using API key from config.json: AIzaSyDQ...yfuk
[2025-09-05 23:02:02] INFO     ✅ Gemini AI client initialized successfully
[2025-09-05 23:02:02] INFO     ✅ AI Vision Monitor initialized for web interface
[2025-09-05 23:02:05] INFO     📸 Manual capture initiated from web interface
[2025-09-05 23:02:05] WARNING  Camera disconnected, attempting to reconnect...
[2025-09-05 23:02:05] INFO     🔌 Connecting to RTSP camera: rtsp://***:***@*************:1935
[2025-09-05 23:02:08] INFO     ✅ Successfully connected to RTSP camera (retry 1, 2 successful reads)
[2025-09-05 23:02:08] INFO     📐 Frame size: (480, 640, 3)
[2025-09-05 23:02:08] INFO     ✅ Image captured and saved: captured_images/capture_20250905_230208.jpg
[2025-09-05 23:02:08] INFO     Image captured successfully: captured_images/capture_20250905_230208.jpg
[2025-09-05 23:02:08] INFO     📷 Image captured: capture_20250905_230208.jpg
[2025-09-05 23:02:08] INFO     🤖 Processing with AI (License Plate Detection)...
[2025-09-05 23:02:08] INFO     Processing image for License Plate Detection: captured_images/capture_20250905_230208.jpg
[2025-09-05 23:02:10] INFO     API call successful: {'success': True, 'license_plates': [], 'plates_detected': 0, 'raw_response': 'LICENSE PLATES DETECTED: 0\nNo license plates visible in this image.', 'image_path': 'captured_images/capture_20250905_230208.jpg'}
[2025-09-05 23:02:10] INFO     No license plates detected in captured_images/capture_20250905_230208.jpg
[2025-09-05 23:02:10] INFO     ℹ️  No license plates detected
[2025-09-05 23:02:14] INFO     🔄 Mode changed to: object_detection
[2025-09-05 23:02:14] INFO     🔄 Detection mode changed to: object_detection
[2025-09-05 23:02:16] INFO     📸 Manual capture initiated from web interface
[2025-09-05 23:02:16] INFO     ✅ Image captured and saved: captured_images/capture_20250905_230216.jpg
[2025-09-05 23:02:16] INFO     Image captured successfully: captured_images/capture_20250905_230216.jpg
[2025-09-05 23:02:16] INFO     📷 Image captured: capture_20250905_230216.jpg
[2025-09-05 23:02:16] INFO     🤖 Processing with AI (Object Detection)...
[2025-09-05 23:02:16] INFO     Processing image for Object Detection: captured_images/capture_20250905_230216.jpg
[2025-09-05 23:02:19] INFO     API call successful: {'success': True, 'mode': 'object_detection', 'description': 'Person in bed with laptop next to wall shelf containing decorations.', 'word_count': 11, 'raw_response': 'Person in bed with laptop next to wall shelf containing decorations.\n', 'image_path': 'captured_images/capture_20250905_230216.jpg'}
[2025-09-05 23:02:19] INFO     👁️  Scene: Person in bed with laptop next to wall shelf containing decorations. (11 words)
[2025-09-05 23:02:19] INFO     SCENE: Person in bed with laptop next to wall shelf containing decorations. | 11 words | capture_20250905_230216.jpg
[2025-09-05 23:02:22] INFO     📸 Manual capture initiated from web interface
[2025-09-05 23:02:22] INFO     ✅ Image captured and saved: captured_images/capture_20250905_230222.jpg
[2025-09-05 23:02:22] INFO     Image captured successfully: captured_images/capture_20250905_230222.jpg
[2025-09-05 23:02:22] INFO     📷 Image captured: capture_20250905_230222.jpg
[2025-09-05 23:02:22] INFO     🤖 Processing with AI (Object Detection)...
[2025-09-05 23:02:22] INFO     Processing image for Object Detection: captured_images/capture_20250905_230222.jpg
[2025-09-05 23:02:24] INFO     API call successful: {'success': True, 'mode': 'object_detection', 'description': 'Person on bed with laptop, shelf on wall with clock and decorations.', 'word_count': 12, 'raw_response': 'Person on bed with laptop, shelf on wall with clock and decorations.\n', 'image_path': 'captured_images/capture_20250905_230222.jpg'}
[2025-09-05 23:02:24] INFO     👁️  Scene: Person on bed with laptop, shelf on wall with clock and decorations. (12 words)
[2025-09-05 23:02:24] INFO     SCENE: Person on bed with laptop, shelf on wall with clock and decorations. | 12 words | capture_20250905_230222.jpg
[2025-09-06 11:13:16] INFO     🔑 Using API key from config.json (ignoring environment variable)
[2025-09-06 11:13:16] INFO     ✅ Configuration loaded from config.json
[2025-09-06 11:13:17] INFO     🎯 Operating in License Plate Detection mode
[2025-09-06 11:13:17] INFO     📹 Initializing real RTSP camera
[2025-09-06 11:13:17] INFO     🔑 Using API key from config.json: AIzaSyDQ...yfuk
[2025-09-06 11:13:17] INFO     ✅ Gemini AI client initialized successfully
[2025-09-06 11:13:17] INFO     ✅ AI Vision Monitor initialized for web interface
[2025-09-06 11:13:27] INFO     📸 Manual capture initiated from web interface
[2025-09-06 11:13:27] WARNING  Camera disconnected, attempting to reconnect...
[2025-09-06 11:13:27] INFO     🔌 Connecting to RTSP camera: rtsp://***:***@*************:1935
[2025-09-06 11:13:27] WARNING  ⚠️ Failed to open RTSP stream (attempt 1)
[2025-09-06 11:13:27] INFO     🔄 Retry 2/3 in 2 seconds...
[2025-09-06 11:13:29] INFO     🔌 Connecting to RTSP camera: rtsp://***:***@*************:1935
[2025-09-06 11:13:29] WARNING  ⚠️ Failed to open RTSP stream (attempt 2)
[2025-09-06 11:13:29] INFO     🔄 Retry 3/3 in 4 seconds...
[2025-09-06 11:13:33] INFO     🔌 Connecting to RTSP camera: rtsp://***:***@*************:1935
[2025-09-06 11:13:33] WARNING  ⚠️ Failed to open RTSP stream (attempt 3)
[2025-09-06 11:13:33] ERROR    ❌ Failed to connect to RTSP camera after 3 attempts
[2025-09-06 11:13:33] ERROR    ❌ Failed to reconnect to camera
[2025-09-06 11:13:33] ERROR    ❌ Capture failed: Failed to connect to camera
[2025-09-06 11:13:54] INFO     📸 Manual capture initiated from web interface
[2025-09-06 11:13:54] WARNING  Camera disconnected, attempting to reconnect...
[2025-09-06 11:13:54] INFO     🔌 Connecting to RTSP camera: rtsp://***:***@*************:1935
[2025-09-06 11:13:54] WARNING  ⚠️ Failed to open RTSP stream (attempt 1)
[2025-09-06 11:13:54] INFO     🔄 Retry 2/3 in 2 seconds...
[2025-09-06 11:13:56] INFO     🔌 Connecting to RTSP camera: rtsp://***:***@*************:1935
[2025-09-06 11:13:57] WARNING  ⚠️ Failed to open RTSP stream (attempt 2)
[2025-09-06 11:13:57] INFO     🔄 Retry 3/3 in 4 seconds...
[2025-09-06 11:14:01] INFO     🔌 Connecting to RTSP camera: rtsp://***:***@*************:1935
[2025-09-06 11:14:01] WARNING  ⚠️ Failed to open RTSP stream (attempt 3)
[2025-09-06 11:14:01] ERROR    ❌ Failed to connect to RTSP camera after 3 attempts
[2025-09-06 11:14:01] ERROR    ❌ Failed to reconnect to camera
[2025-09-06 11:14:01] ERROR    ❌ Capture failed: Failed to connect to camera
[2025-09-06 11:16:53] INFO     📸 Manual capture initiated from web interface
[2025-09-06 11:16:53] WARNING  Camera disconnected, attempting to reconnect...
[2025-09-06 11:16:53] INFO     🔌 Connecting to RTSP camera: rtsp://***:***@*************:1935
[2025-09-06 11:16:56] INFO     ✅ Successfully connected to RTSP camera (retry 1, 2 successful reads)
[2025-09-06 11:16:56] INFO     📐 Frame size: (480, 640, 3)
[2025-09-06 11:16:56] INFO     ✅ Image captured and saved: captured_images/capture_20250906_111656.jpg
[2025-09-06 11:16:56] INFO     Image captured successfully: captured_images/capture_20250906_111656.jpg
[2025-09-06 11:16:56] INFO     📷 Image captured: capture_20250906_111656.jpg
[2025-09-06 11:16:56] INFO     🤖 Processing with AI (License Plate Detection)...
[2025-09-06 11:16:56] INFO     Processing image for License Plate Detection: captured_images/capture_20250906_111656.jpg
[2025-09-06 11:16:59] INFO     API call successful: {'success': True, 'license_plates': [], 'plates_detected': 0, 'raw_response': 'LICENSE PLATES DETECTED: 0\nNo license plates visible in this image.', 'image_path': 'captured_images/capture_20250906_111656.jpg'}
[2025-09-06 11:16:59] INFO     No license plates detected in captured_images/capture_20250906_111656.jpg
[2025-09-06 11:16:59] INFO     ℹ️  No license plates detected
[2025-09-06 11:17:04] INFO     🔄 Mode changed to: object_detection
[2025-09-06 11:17:04] INFO     🔄 Detection mode changed to: object_detection
[2025-09-06 11:17:05] INFO     📸 Manual capture initiated from web interface
[2025-09-06 11:17:05] INFO     ✅ Image captured and saved: captured_images/capture_20250906_111705.jpg
[2025-09-06 11:17:05] INFO     Image captured successfully: captured_images/capture_20250906_111705.jpg
[2025-09-06 11:17:05] INFO     📷 Image captured: capture_20250906_111705.jpg
[2025-09-06 11:17:05] INFO     🤖 Processing with AI (Object Detection)...
[2025-09-06 11:17:05] INFO     Processing image for Object Detection: captured_images/capture_20250906_111705.jpg
[2025-09-06 11:17:07] INFO     API call successful: {'success': True, 'mode': 'object_detection', 'description': 'The person is in front of open closet, and the desk is reflected.', 'word_count': 13, 'raw_response': 'The person is in front of open closet, and the desk is reflected.\n', 'image_path': 'captured_images/capture_20250906_111705.jpg'}
[2025-09-06 11:17:07] INFO     👁️  Scene: The person is in front of open closet, and the desk is reflected. (13 words)
[2025-09-06 11:17:07] INFO     SCENE: The person is in front of open closet, and the desk is reflected. | 13 words | capture_20250906_111705.jpg
[2025-09-06 11:17:09] INFO     📸 Manual capture initiated from web interface
[2025-09-06 11:17:09] INFO     ✅ Image captured and saved: captured_images/capture_20250906_111709.jpg
[2025-09-06 11:17:09] INFO     Image captured successfully: captured_images/capture_20250906_111709.jpg
[2025-09-06 11:17:09] INFO     📷 Image captured: capture_20250906_111709.jpg
[2025-09-06 11:17:09] INFO     🤖 Processing with AI (Object Detection)...
[2025-09-06 11:17:09] INFO     Processing image for Object Detection: captured_images/capture_20250906_111709.jpg
[2025-09-06 11:17:11] INFO     API call successful: {'success': True, 'mode': 'object_detection', 'description': 'Person sits near wardrobe, desk, and office chair in room.', 'word_count': 10, 'raw_response': 'Person sits near wardrobe, desk, and office chair in room.\n', 'image_path': 'captured_images/capture_20250906_111709.jpg'}
[2025-09-06 11:17:11] INFO     👁️  Scene: Person sits near wardrobe, desk, and office chair in room. (10 words)
[2025-09-06 11:17:11] INFO     SCENE: Person sits near wardrobe, desk, and office chair in room. | 10 words | capture_20250906_111709.jpg
[2025-09-06 11:18:16] INFO     📸 Manual capture initiated from web interface
[2025-09-06 11:18:16] INFO     ✅ Image captured and saved: captured_images/capture_20250906_111816.jpg
[2025-09-06 11:18:16] INFO     Image captured successfully: captured_images/capture_20250906_111816.jpg
[2025-09-06 11:18:16] INFO     📷 Image captured: capture_20250906_111816.jpg
[2025-09-06 11:18:16] INFO     🤖 Processing with AI (Object Detection)...
[2025-09-06 11:18:16] INFO     Processing image for Object Detection: captured_images/capture_20250906_111816.jpg
[2025-09-06 11:18:19] INFO     API call successful: {'success': True, 'mode': 'object_detection', 'description': 'Person sitting on the floor near open closet and desk in messy room.', 'word_count': 13, 'raw_response': 'Person sitting on the floor near open closet and desk in messy room.\n', 'image_path': 'captured_images/capture_20250906_111816.jpg'}
[2025-09-06 11:18:19] INFO     👁️  Scene: Person sitting on the floor near open closet and desk in messy room. (13 words)
[2025-09-06 11:18:19] INFO     SCENE: Person sitting on the floor near open closet and desk in messy room. | 13 words | capture_20250906_111816.jpg
[2025-09-06 11:23:56] INFO     📸 Manual capture initiated from web interface
[2025-09-06 11:23:56] INFO     ✅ Image captured and saved: captured_images/capture_20250906_112356.jpg
[2025-09-06 11:23:56] INFO     Image captured successfully: captured_images/capture_20250906_112356.jpg
[2025-09-06 11:23:56] INFO     📷 Image captured: capture_20250906_112356.jpg
[2025-09-06 11:23:56] INFO     🤖 Processing with AI (Object Detection)...
[2025-09-06 11:23:56] INFO     Processing image for Object Detection: captured_images/capture_20250906_112356.jpg
[2025-09-06 11:23:58] INFO     API call successful: {'success': True, 'mode': 'object_detection', 'description': 'Person in cluttered room with a computer, desk, chair, and wardrobe.', 'word_count': 11, 'raw_response': 'Person in cluttered room with a computer, desk, chair, and wardrobe.\n', 'image_path': 'captured_images/capture_20250906_112356.jpg'}
[2025-09-06 11:23:58] INFO     👁️  Scene: Person in cluttered room with a computer, desk, chair, and wardrobe. (11 words)
[2025-09-06 11:23:58] INFO     SCENE: Person in cluttered room with a computer, desk, chair, and wardrobe. | 11 words | capture_20250906_112356.jpg
[2025-09-06 11:24:16] INFO     Received signal 2, shutting down gracefully...
[2025-09-06 11:24:16] INFO     🛑 Stopping License Plate Monitoring System...
[2025-09-06 11:24:16] INFO     🔌 Disconnected from RTSP camera
[2025-09-06 11:24:16] INFO     ✅ License Plate Monitoring System stopped
[2025-09-06 11:25:38] INFO     🔑 Using API key from config.json (ignoring environment variable)
[2025-09-06 11:25:38] INFO     ✅ Configuration loaded from config.json
[2025-09-06 11:25:39] INFO     🎯 Operating in License Plate Detection mode
[2025-09-06 11:25:39] INFO     📹 Initializing real RTSP camera
[2025-09-06 11:25:39] INFO     🔑 Using API key from config.json: AIzaSyDQ...yfuk
[2025-09-06 11:25:39] INFO     ✅ Gemini AI client initialized successfully
[2025-09-06 11:25:39] INFO     ✅ AI Vision Monitor initialized for web interface
[2025-09-06 11:25:43] INFO     📸 Manual capture initiated from web interface
[2025-09-06 11:25:43] INFO     🔄 Using fresh capture mode for manual trigger
[2025-09-06 11:25:43] INFO     🔄 Forcing fresh capture with camera reconnection...
[2025-09-06 11:25:43] INFO     🔌 Disconnected from RTSP camera
[2025-09-06 11:25:43] INFO     🔌 Connecting to RTSP camera: rtsp://***:***@*************:1935
[2025-09-06 11:25:44] WARNING  ⚠️ Failed to open RTSP stream (attempt 1)
[2025-09-06 11:25:44] INFO     🔄 Retry 2/3 in 2 seconds...
[2025-09-06 11:25:46] INFO     🔌 Connecting to RTSP camera: rtsp://***:***@*************:1935
[2025-09-06 11:25:46] WARNING  ⚠️ Failed to open RTSP stream (attempt 2)
[2025-09-06 11:25:46] INFO     🔄 Retry 3/3 in 4 seconds...
[2025-09-06 11:25:49] INFO     🔄 Mode changed to: object_detection
[2025-09-06 11:25:49] INFO     🔄 Detection mode changed to: object_detection
[2025-09-06 11:25:50] INFO     🔌 Connecting to RTSP camera: rtsp://***:***@*************:1935
[2025-09-06 11:25:50] WARNING  ⚠️ Failed to open RTSP stream (attempt 3)
[2025-09-06 11:25:50] ERROR    ❌ Failed to connect to RTSP camera after 3 attempts
[2025-09-06 11:25:50] ERROR    ❌ Failed to reconnect for fresh capture
[2025-09-06 11:25:50] ERROR    ❌ Failed to capture fresh image
[2025-09-06 11:25:50] ERROR    ❌ Capture failed: Failed to capture fresh image
[2025-09-06 11:25:53] INFO     📸 Manual capture initiated from web interface
[2025-09-06 11:25:53] INFO     🔄 Using fresh capture mode for manual trigger
[2025-09-06 11:25:53] INFO     🔄 Forcing fresh capture with camera reconnection...
[2025-09-06 11:25:53] INFO     🔌 Disconnected from RTSP camera
[2025-09-06 11:25:53] INFO     🔌 Connecting to RTSP camera: rtsp://***:***@*************:1935
[2025-09-06 11:25:53] WARNING  ⚠️ Failed to open RTSP stream (attempt 1)
[2025-09-06 11:25:53] INFO     🔄 Retry 2/3 in 2 seconds...
[2025-09-06 11:25:55] INFO     🔌 Connecting to RTSP camera: rtsp://***:***@*************:1935
[2025-09-06 11:25:55] WARNING  ⚠️ Failed to open RTSP stream (attempt 2)
[2025-09-06 11:25:55] INFO     🔄 Retry 3/3 in 4 seconds...
[2025-09-06 11:25:59] INFO     🔌 Connecting to RTSP camera: rtsp://***:***@*************:1935
[2025-09-06 11:25:59] WARNING  ⚠️ Failed to open RTSP stream (attempt 3)
[2025-09-06 11:25:59] ERROR    ❌ Failed to connect to RTSP camera after 3 attempts
[2025-09-06 11:25:59] ERROR    ❌ Failed to reconnect for fresh capture
[2025-09-06 11:25:59] ERROR    ❌ Failed to capture fresh image
[2025-09-06 11:25:59] ERROR    ❌ Capture failed: Failed to capture fresh image
[2025-09-06 11:26:21] INFO     📸 Manual capture initiated from web interface
[2025-09-06 11:26:21] INFO     🔄 Using fresh capture mode for manual trigger
[2025-09-06 11:26:21] INFO     🔄 Forcing fresh capture with camera reconnection...
[2025-09-06 11:26:21] INFO     🔌 Disconnected from RTSP camera
[2025-09-06 11:26:21] INFO     🔌 Connecting to RTSP camera: rtsp://***:***@*************:1935
[2025-09-06 11:26:25] INFO     ✅ Successfully connected to RTSP camera (retry 1, 2 successful reads)
[2025-09-06 11:26:25] INFO     📐 Frame size: (480, 640, 3)
[2025-09-06 11:26:26] INFO     ✅ Image captured and saved: captured_images/capture_20250906_112626_212516.jpg
[2025-09-06 11:26:26] INFO     Image captured successfully: captured_images/capture_20250906_112626_212516.jpg
[2025-09-06 11:26:26] INFO     📷 Fresh image captured: capture_20250906_112626_212516.jpg
[2025-09-06 11:26:26] INFO     🤖 Processing with AI (Object Detection)...
[2025-09-06 11:26:26] INFO     Processing image for Object Detection: captured_images/capture_20250906_112626_212516.jpg
[2025-09-06 11:26:28] INFO     API call successful: {'success': True, 'mode': 'object_detection', 'description': 'Man sitting at desk using keyboard and mouse with phone and water bottle.', 'word_count': 13, 'raw_response': 'Man sitting at desk using keyboard and mouse with phone and water bottle.\n', 'image_path': 'captured_images/capture_20250906_112626_212516.jpg'}
[2025-09-06 11:26:28] INFO     👁️  Scene: Man sitting at desk using keyboard and mouse with phone and water bottle. (13 words)
[2025-09-06 11:26:28] INFO     SCENE: Man sitting at desk using keyboard and mouse with phone and water bottle. | 13 words | capture_20250906_112626_212516.jpg
[2025-09-06 11:26:34] INFO     📸 Manual capture initiated from web interface
[2025-09-06 11:26:34] INFO     🔄 Using fresh capture mode for manual trigger
[2025-09-06 11:26:34] INFO     🔄 Forcing fresh capture with camera reconnection...
[2025-09-06 11:26:34] INFO     🔌 Disconnected from RTSP camera
[2025-09-06 11:26:35] INFO     🔌 Connecting to RTSP camera: rtsp://***:***@*************:1935
[2025-09-06 11:26:35] WARNING  ⚠️ Failed to open RTSP stream (attempt 1)
[2025-09-06 11:26:35] INFO     🔄 Retry 2/3 in 2 seconds...
[2025-09-06 11:26:37] INFO     🔌 Connecting to RTSP camera: rtsp://***:***@*************:1935
[2025-09-06 11:26:37] WARNING  ⚠️ Failed to open RTSP stream (attempt 2)
[2025-09-06 11:26:37] INFO     🔄 Retry 3/3 in 4 seconds...
[2025-09-06 11:26:41] INFO     🔌 Connecting to RTSP camera: rtsp://***:***@*************:1935
[2025-09-06 11:26:41] WARNING  ⚠️ Failed to open RTSP stream (attempt 3)
[2025-09-06 11:26:41] ERROR    ❌ Failed to connect to RTSP camera after 3 attempts
[2025-09-06 11:26:41] ERROR    ❌ Failed to reconnect for fresh capture
[2025-09-06 11:26:41] ERROR    ❌ Failed to capture fresh image
[2025-09-06 11:26:41] ERROR    ❌ Capture failed: Failed to capture fresh image
[2025-09-06 11:26:47] INFO     📸 Manual capture initiated from web interface
[2025-09-06 11:26:47] INFO     🔄 Using fresh capture mode for manual trigger
[2025-09-06 11:26:47] INFO     🔄 Forcing fresh capture with camera reconnection...
[2025-09-06 11:26:47] INFO     🔌 Disconnected from RTSP camera
[2025-09-06 11:26:48] INFO     🔌 Connecting to RTSP camera: rtsp://***:***@*************:1935
[2025-09-06 11:26:48] WARNING  ⚠️ Failed to open RTSP stream (attempt 1)
[2025-09-06 11:26:48] INFO     🔄 Retry 2/3 in 2 seconds...
[2025-09-06 11:26:50] INFO     🔌 Connecting to RTSP camera: rtsp://***:***@*************:1935
[2025-09-06 11:26:50] WARNING  ⚠️ Failed to open RTSP stream (attempt 2)
[2025-09-06 11:26:50] INFO     🔄 Retry 3/3 in 4 seconds...
[2025-09-06 11:26:54] INFO     🔌 Connecting to RTSP camera: rtsp://***:***@*************:1935
[2025-09-06 11:26:54] WARNING  ⚠️ Failed to open RTSP stream (attempt 3)
[2025-09-06 11:26:54] ERROR    ❌ Failed to connect to RTSP camera after 3 attempts
[2025-09-06 11:26:54] ERROR    ❌ Failed to reconnect for fresh capture
[2025-09-06 11:26:54] ERROR    ❌ Failed to capture fresh image
[2025-09-06 11:26:54] ERROR    ❌ Capture failed: Failed to capture fresh image
[2025-09-06 11:28:04] INFO     Received signal 2, shutting down gracefully...
[2025-09-06 11:28:04] INFO     🛑 Stopping License Plate Monitoring System...
[2025-09-06 11:28:04] INFO     🔌 Disconnected from RTSP camera
[2025-09-06 11:28:04] INFO     ✅ License Plate Monitoring System stopped
[2025-09-06 11:28:05] INFO     Received signal 2, shutting down gracefully...
[2025-09-06 11:28:05] INFO     🛑 Stopping License Plate Monitoring System...
[2025-09-06 11:28:05] INFO     🔌 Disconnected from RTSP camera
[2025-09-06 11:28:05] INFO     ✅ License Plate Monitoring System stopped
[2025-09-06 11:28:05] INFO     Received signal 2, shutting down gracefully...
[2025-09-06 11:28:05] INFO     🛑 Stopping License Plate Monitoring System...
[2025-09-06 11:28:05] INFO     🔌 Disconnected from RTSP camera
[2025-09-06 11:28:05] INFO     ✅ License Plate Monitoring System stopped
[2025-09-06 11:28:05] INFO     Received signal 2, shutting down gracefully...
[2025-09-06 11:28:05] INFO     🛑 Stopping License Plate Monitoring System...
[2025-09-06 11:28:05] INFO     🔌 Disconnected from RTSP camera
[2025-09-06 11:28:05] INFO     ✅ License Plate Monitoring System stopped
[2025-09-06 11:28:05] INFO     Received signal 2, shutting down gracefully...
[2025-09-06 11:28:05] INFO     🛑 Stopping License Plate Monitoring System...
[2025-09-06 11:28:05] INFO     🔌 Disconnected from RTSP camera
[2025-09-06 11:28:05] INFO     ✅ License Plate Monitoring System stopped
[2025-09-06 11:28:05] INFO     Received signal 2, shutting down gracefully...
[2025-09-06 11:28:05] INFO     🛑 Stopping License Plate Monitoring System...
[2025-09-06 11:28:05] INFO     🔌 Disconnected from RTSP camera
[2025-09-06 11:28:05] INFO     ✅ License Plate Monitoring System stopped
[2025-09-06 11:28:05] INFO     Received signal 2, shutting down gracefully...
[2025-09-06 11:28:05] INFO     🛑 Stopping License Plate Monitoring System...
[2025-09-06 11:28:05] INFO     🔌 Disconnected from RTSP camera
[2025-09-06 11:28:05] INFO     ✅ License Plate Monitoring System stopped
[2025-09-06 11:28:05] INFO     Received signal 2, shutting down gracefully...
[2025-09-06 11:28:05] INFO     🛑 Stopping License Plate Monitoring System...
[2025-09-06 11:28:05] INFO     🔌 Disconnected from RTSP camera
[2025-09-06 11:28:05] INFO     ✅ License Plate Monitoring System stopped
[2025-09-06 11:28:05] INFO     Received signal 2, shutting down gracefully...
[2025-09-06 11:28:05] INFO     🛑 Stopping License Plate Monitoring System...
[2025-09-06 11:28:05] INFO     🔌 Disconnected from RTSP camera
[2025-09-06 11:28:05] INFO     ✅ License Plate Monitoring System stopped
[2025-09-06 11:28:05] INFO     Received signal 2, shutting down gracefully...
[2025-09-06 11:28:05] INFO     🛑 Stopping License Plate Monitoring System...
[2025-09-06 11:28:05] INFO     🔌 Disconnected from RTSP camera
[2025-09-06 11:28:05] INFO     ✅ License Plate Monitoring System stopped
[2025-09-06 11:28:05] INFO     Received signal 2, shutting down gracefully...
[2025-09-06 11:28:05] INFO     🛑 Stopping License Plate Monitoring System...
[2025-09-06 11:28:05] INFO     🔌 Disconnected from RTSP camera
[2025-09-06 11:28:05] INFO     ✅ License Plate Monitoring System stopped
[2025-09-06 11:28:05] INFO     Received signal 2, shutting down gracefully...
[2025-09-06 11:28:05] INFO     🛑 Stopping License Plate Monitoring System...
[2025-09-06 11:28:05] INFO     🔌 Disconnected from RTSP camera
[2025-09-06 11:28:05] INFO     ✅ License Plate Monitoring System stopped
[2025-09-06 11:28:05] INFO     Received signal 2, shutting down gracefully...
[2025-09-06 11:28:05] INFO     🛑 Stopping License Plate Monitoring System...
[2025-09-06 11:28:05] INFO     🔌 Disconnected from RTSP camera
[2025-09-06 11:28:05] INFO     ✅ License Plate Monitoring System stopped
[2025-09-06 11:28:05] INFO     Received signal 2, shutting down gracefully...
[2025-09-06 11:28:05] INFO     🛑 Stopping License Plate Monitoring System...
[2025-09-06 11:28:05] INFO     🔌 Disconnected from RTSP camera
[2025-09-06 11:28:05] INFO     ✅ License Plate Monitoring System stopped
[2025-09-06 11:28:05] INFO     Received signal 2, shutting down gracefully...
[2025-09-06 11:28:05] INFO     🛑 Stopping License Plate Monitoring System...
[2025-09-06 11:28:05] INFO     🔌 Disconnected from RTSP camera
[2025-09-06 11:28:05] INFO     ✅ License Plate Monitoring System stopped
[2025-09-06 11:28:05] INFO     Received signal 2, shutting down gracefully...
[2025-09-06 11:28:05] INFO     🛑 Stopping License Plate Monitoring System...
[2025-09-06 11:28:05] INFO     🔌 Disconnected from RTSP camera
[2025-09-06 11:28:05] INFO     ✅ License Plate Monitoring System stopped
[2025-09-06 11:28:05] INFO     Received signal 2, shutting down gracefully...
[2025-09-06 11:28:05] INFO     🛑 Stopping License Plate Monitoring System...
[2025-09-06 11:28:05] INFO     🔌 Disconnected from RTSP camera
[2025-09-06 11:28:05] INFO     ✅ License Plate Monitoring System stopped
[2025-09-06 11:28:05] INFO     Received signal 2, shutting down gracefully...
[2025-09-06 11:28:05] INFO     🛑 Stopping License Plate Monitoring System...
[2025-09-06 11:28:05] INFO     🔌 Disconnected from RTSP camera
[2025-09-06 11:28:05] INFO     ✅ License Plate Monitoring System stopped
[2025-09-06 11:28:05] INFO     Received signal 2, shutting down gracefully...
[2025-09-06 11:28:05] INFO     🛑 Stopping License Plate Monitoring System...
[2025-09-06 11:28:05] INFO     🔌 Disconnected from RTSP camera
[2025-09-06 11:28:05] INFO     ✅ License Plate Monitoring System stopped
[2025-09-06 11:28:06] INFO     Received signal 2, shutting down gracefully...
[2025-09-06 11:28:06] INFO     🛑 Stopping License Plate Monitoring System...
[2025-09-06 11:28:06] INFO     🔌 Disconnected from RTSP camera
[2025-09-06 11:28:06] INFO     ✅ License Plate Monitoring System stopped
[2025-09-06 11:28:06] INFO     Received signal 2, shutting down gracefully...
[2025-09-06 11:28:06] INFO     🛑 Stopping License Plate Monitoring System...
[2025-09-06 11:28:06] INFO     🔌 Disconnected from RTSP camera
[2025-09-06 11:28:06] INFO     ✅ License Plate Monitoring System stopped
[2025-09-06 11:28:06] INFO     Received signal 2, shutting down gracefully...
[2025-09-06 11:28:06] INFO     🛑 Stopping License Plate Monitoring System...
[2025-09-06 11:28:06] INFO     🔌 Disconnected from RTSP camera
[2025-09-06 11:28:06] INFO     ✅ License Plate Monitoring System stopped
[2025-09-06 11:28:06] INFO     Received signal 2, shutting down gracefully...
[2025-09-06 11:28:06] INFO     🛑 Stopping License Plate Monitoring System...
[2025-09-06 11:28:06] INFO     🔌 Disconnected from RTSP camera
[2025-09-06 11:28:06] INFO     ✅ License Plate Monitoring System stopped
[2025-09-06 11:28:36] INFO     🔑 Using API key from config.json (ignoring environment variable)
[2025-09-06 11:28:36] INFO     ✅ Configuration loaded from config.json
[2025-09-06 11:28:37] INFO     🎯 Operating in License Plate Detection mode
[2025-09-06 11:28:37] INFO     📹 Initializing real RTSP camera
[2025-09-06 11:28:37] INFO     🔑 Using API key from config.json: AIzaSyDQ...yfuk
[2025-09-06 11:28:37] INFO     ✅ Gemini AI client initialized successfully
[2025-09-06 11:28:37] INFO     ✅ AI Vision Monitor initialized for web interface
[2025-09-06 11:29:37] INFO     🔑 Using API key from config.json (ignoring environment variable)
[2025-09-06 11:29:37] INFO     ✅ Configuration loaded from config.json
[2025-09-06 11:29:38] INFO     🎯 Operating in License Plate Detection mode
[2025-09-06 11:29:38] INFO     📹 Initializing real RTSP camera
[2025-09-06 11:29:38] INFO     🔑 Using API key from config.json: AIzaSyDQ...yfuk
[2025-09-06 11:29:38] INFO     ✅ Gemini AI client initialized successfully
[2025-09-06 11:29:38] INFO     ✅ AI Vision Monitor initialized for web interface
[2025-09-06 11:29:46] INFO     📸 Manual capture initiated from web interface
[2025-09-06 11:29:46] INFO     🔄 Using fresh capture mode for manual trigger
[2025-09-06 11:29:46] INFO     🔄 Forcing fresh capture with camera reconnection...
[2025-09-06 11:29:46] INFO     🔌 Disconnected from RTSP camera
[2025-09-06 11:29:46] INFO     🔌 Connecting to RTSP camera: rtsp://***:***@*************:1935
[2025-09-06 11:29:46] WARNING  ⚠️ Failed to open RTSP stream (attempt 1)
[2025-09-06 11:29:46] INFO     🔄 Retry 2/3 in 2 seconds...
[2025-09-06 11:29:48] INFO     🔌 Connecting to RTSP camera: rtsp://***:***@*************:1935
[2025-09-06 11:29:48] WARNING  ⚠️ Failed to open RTSP stream (attempt 2)
[2025-09-06 11:29:48] INFO     🔄 Retry 3/3 in 4 seconds...
[2025-09-06 11:29:49] INFO     🔄 Mode changed to: object_detection
[2025-09-06 11:29:49] INFO     🔄 Detection mode changed to: object_detection
[2025-09-06 11:29:52] INFO     🔌 Connecting to RTSP camera: rtsp://***:***@*************:1935
[2025-09-06 11:29:53] WARNING  ⚠️ Failed to open RTSP stream (attempt 3)
[2025-09-06 11:29:53] ERROR    ❌ Failed to connect to RTSP camera after 3 attempts
[2025-09-06 11:29:53] ERROR    ❌ Failed to reconnect for fresh capture
[2025-09-06 11:29:53] ERROR    ❌ Failed to capture fresh image
[2025-09-06 11:29:53] ERROR    ❌ Capture failed: Failed to capture fresh image
[2025-09-06 11:30:00] INFO     📸 Manual capture initiated from web interface
[2025-09-06 11:30:00] INFO     🔄 Using fresh capture mode for manual trigger
[2025-09-06 11:30:00] INFO     🔄 Forcing fresh capture with camera reconnection...
[2025-09-06 11:30:00] INFO     🔌 Disconnected from RTSP camera
[2025-09-06 11:30:01] INFO     🔌 Connecting to RTSP camera: rtsp://***:***@*************:1935
[2025-09-06 11:30:03] INFO     ✅ Successfully connected to RTSP camera (retry 1, 2 successful reads)
[2025-09-06 11:30:03] INFO     📐 Frame size: (480, 640, 3)
[2025-09-06 11:30:03] INFO     ✅ Image captured and saved: captured_images/capture_20250906_113003_700903.jpg
[2025-09-06 11:30:03] INFO     Image captured successfully: captured_images/capture_20250906_113003_700903.jpg
[2025-09-06 11:30:03] INFO     📷 Fresh image captured: capture_20250906_113003_700903.jpg
[2025-09-06 11:30:03] INFO     🤖 Processing with AI (Object Detection)...
[2025-09-06 11:30:03] INFO     Processing image for Object Detection: captured_images/capture_20250906_113003_700903.jpg
[2025-09-06 11:30:06] INFO     API call successful: {'success': True, 'mode': 'object_detection', 'description': 'Person using computer with reflection in glass surface.', 'word_count': 8, 'raw_response': 'Person using computer with reflection in glass surface.\n', 'image_path': 'captured_images/capture_20250906_113003_700903.jpg'}
[2025-09-06 11:30:06] INFO     👁️  Scene: Person using computer with reflection in glass surface. (8 words)
[2025-09-06 11:30:06] INFO     SCENE: Person using computer with reflection in glass surface. | 8 words | capture_20250906_113003_700903.jpg
[2025-09-06 11:30:14] INFO     📸 Manual capture initiated from web interface
[2025-09-06 11:30:14] INFO     🔄 Using fresh capture mode for manual trigger
[2025-09-06 11:30:14] INFO     🔄 Forcing fresh capture with camera reconnection...
[2025-09-06 11:30:14] INFO     🔌 Disconnected from RTSP camera
[2025-09-06 11:30:15] INFO     🔌 Connecting to RTSP camera: rtsp://***:***@*************:1935
[2025-09-06 11:30:15] WARNING  ⚠️ Failed to open RTSP stream (attempt 1)
[2025-09-06 11:30:15] INFO     🔄 Retry 2/3 in 2 seconds...
[2025-09-06 11:30:17] INFO     🔌 Connecting to RTSP camera: rtsp://***:***@*************:1935
[2025-09-06 11:30:17] WARNING  ⚠️ Failed to open RTSP stream (attempt 2)
[2025-09-06 11:30:17] INFO     🔄 Retry 3/3 in 4 seconds...
[2025-09-06 11:30:21] INFO     🔌 Connecting to RTSP camera: rtsp://***:***@*************:1935
[2025-09-06 11:30:21] WARNING  ⚠️ Failed to open RTSP stream (attempt 3)
[2025-09-06 11:30:21] ERROR    ❌ Failed to connect to RTSP camera after 3 attempts
[2025-09-06 11:30:21] ERROR    ❌ Failed to reconnect for fresh capture
[2025-09-06 11:30:21] ERROR    ❌ Failed to capture fresh image
[2025-09-06 11:30:21] ERROR    ❌ Capture failed: Failed to capture fresh image
[2025-09-06 11:30:43] INFO     📸 Manual capture initiated from web interface
[2025-09-06 11:30:43] INFO     🔄 Using fresh capture mode for manual trigger
[2025-09-06 11:30:43] INFO     🔄 Forcing fresh capture with camera reconnection...
[2025-09-06 11:30:43] INFO     🔌 Disconnected from RTSP camera
[2025-09-06 11:30:44] INFO     🔌 Connecting to RTSP camera: rtsp://***:***@*************:1935
[2025-09-06 11:30:47] INFO     ✅ Successfully connected to RTSP camera (retry 1, 2 successful reads)
[2025-09-06 11:30:47] INFO     📐 Frame size: (480, 640, 3)
[2025-09-06 11:30:47] INFO     ✅ Image captured and saved: captured_images/capture_20250906_113047_908411.jpg
[2025-09-06 11:30:47] INFO     Image captured successfully: captured_images/capture_20250906_113047_908411.jpg
[2025-09-06 11:30:47] INFO     📷 Fresh image captured: capture_20250906_113047_908411.jpg
[2025-09-06 11:30:47] INFO     🤖 Processing with AI (Object Detection)...
[2025-09-06 11:30:47] INFO     Processing image for Object Detection: captured_images/capture_20250906_113047_908411.jpg
[2025-09-06 11:30:49] INFO     API call successful: {'success': True, 'mode': 'object_detection', 'description': 'Person looking at the camera near a curtain.', 'word_count': 8, 'raw_response': 'Person looking at the camera near a curtain.', 'image_path': 'captured_images/capture_20250906_113047_908411.jpg'}
[2025-09-06 11:30:49] INFO     👁️  Scene: Person looking at the camera near a curtain. (8 words)
[2025-09-06 11:30:49] INFO     SCENE: Person looking at the camera near a curtain. | 8 words | capture_20250906_113047_908411.jpg
[2025-09-06 11:31:05] INFO     📸 Manual capture initiated from web interface
[2025-09-06 11:31:05] INFO     🔄 Using fresh capture mode for manual trigger
[2025-09-06 11:31:05] INFO     🔄 Forcing fresh capture with camera reconnection...
[2025-09-06 11:31:05] INFO     🔌 Disconnected from RTSP camera
[2025-09-06 11:31:05] INFO     🔌 Connecting to RTSP camera: rtsp://***:***@*************:1935
[2025-09-06 11:31:08] INFO     ✅ Successfully connected to RTSP camera (retry 1, 2 successful reads)
[2025-09-06 11:31:08] INFO     📐 Frame size: (480, 640, 3)
[2025-09-06 11:31:08] INFO     ✅ Image captured and saved: captured_images/capture_20250906_113108_566639.jpg
[2025-09-06 11:31:08] INFO     Image captured successfully: captured_images/capture_20250906_113108_566639.jpg
[2025-09-06 11:31:08] INFO     📷 Fresh image captured: capture_20250906_113108_566639.jpg
[2025-09-06 11:31:08] INFO     🤖 Processing with AI (Object Detection)...
[2025-09-06 11:31:08] INFO     Processing image for Object Detection: captured_images/capture_20250906_113108_566639.jpg
[2025-09-06 11:31:11] INFO     API call successful: {'success': True, 'mode': 'object_detection', 'description': 'Laptop, speaker, and worksheet are present on the table.', 'word_count': 9, 'raw_response': 'Laptop, speaker, and worksheet are present on the table.', 'image_path': 'captured_images/capture_20250906_113108_566639.jpg'}
[2025-09-06 11:31:11] INFO     👁️  Scene: Laptop, speaker, and worksheet are present on the table. (9 words)
[2025-09-06 11:31:11] INFO     SCENE: Laptop, speaker, and worksheet are present on the table. | 9 words | capture_20250906_113108_566639.jpg
[2025-09-06 11:31:24] INFO     Received signal 2, shutting down gracefully...
[2025-09-06 11:31:24] INFO     🛑 Stopping License Plate Monitoring System...
[2025-09-06 11:31:24] INFO     🔌 Disconnected from RTSP camera
[2025-09-06 11:31:24] INFO     ✅ License Plate Monitoring System stopped
[2025-09-06 11:31:41] INFO     🔑 Using API key from config.json (ignoring environment variable)
[2025-09-06 11:31:41] INFO     ✅ Configuration loaded from config.json
[2025-09-06 11:31:42] INFO     🎯 Operating in License Plate Detection mode
[2025-09-06 11:31:42] INFO     📹 Initializing real RTSP camera
[2025-09-06 11:31:42] INFO     🔑 Using API key from config.json: AIzaSyDQ...yfuk
[2025-09-06 11:31:42] INFO     ✅ Gemini AI client initialized successfully
[2025-09-06 11:31:42] INFO     ✅ AI Vision Monitor initialized for web interface
[2025-09-06 11:31:53] INFO     🔄 Mode changed to: object_detection
[2025-09-06 11:31:53] INFO     🔄 Detection mode changed to: object_detection
[2025-09-06 11:31:57] INFO     📸 Manual capture initiated from web interface
[2025-09-06 11:31:57] INFO     🔄 Using fresh capture mode for manual trigger
[2025-09-06 11:31:57] INFO     🔄 Forcing fresh capture with camera reconnection...
[2025-09-06 11:31:57] INFO     🔌 Disconnected from RTSP camera
[2025-09-06 11:31:57] INFO     🔌 Connecting to RTSP camera: rtsp://***:***@*************:1935
[2025-09-06 11:32:01] INFO     ✅ Successfully connected to RTSP camera (retry 1, 2 successful reads)
[2025-09-06 11:32:01] INFO     📐 Frame size: (480, 640, 3)
[2025-09-06 11:32:01] INFO     ✅ Image captured and saved: captured_images/capture_20250906_113201_646537.jpg
[2025-09-06 11:32:01] INFO     Image captured successfully: captured_images/capture_20250906_113201_646537.jpg
[2025-09-06 11:32:01] INFO     📷 Fresh image captured: capture_20250906_113201_646537.jpg
[2025-09-06 11:32:01] INFO     🤖 Processing with AI (Object Detection)...
[2025-09-06 11:32:01] INFO     Processing image for Object Detection: captured_images/capture_20250906_113201_646537.jpg
[2025-09-06 11:32:04] INFO     API call successful: {'success': True, 'mode': 'object_detection', 'description': 'The room contains a blue cabinet, a hat, monitors, a shelf and a clock.', 'word_count': 14, 'raw_response': 'The room contains a blue cabinet, a hat, monitors, a shelf and a clock.\n', 'image_path': 'captured_images/capture_20250906_113201_646537.jpg'}
[2025-09-06 11:32:04] INFO     👁️  Scene: The room contains a blue cabinet, a hat, monitors, a shelf and a clock. (14 words)
[2025-09-06 11:32:04] INFO     SCENE: The room contains a blue cabinet, a hat, monitors, a shelf and a clock. | 14 words | capture_20250906_113201_646537.jpg
[2025-09-06 11:32:22] INFO     🔄 Mode changed to: license_plate
[2025-09-06 11:32:22] INFO     🔄 Detection mode changed to: license_plate
[2025-09-06 11:32:31] INFO     📸 Manual capture initiated from web interface
[2025-09-06 11:32:31] INFO     🔄 Using fresh capture mode for manual trigger
[2025-09-06 11:32:31] INFO     🔄 Forcing fresh capture with camera reconnection...
[2025-09-06 11:32:31] INFO     🔌 Disconnected from RTSP camera
[2025-09-06 11:32:32] INFO     🔌 Connecting to RTSP camera: rtsp://***:***@*************:1935
[2025-09-06 11:32:32] WARNING  ⚠️ Failed to open RTSP stream (attempt 1)
[2025-09-06 11:32:32] INFO     🔄 Retry 2/3 in 2 seconds...
[2025-09-06 11:32:34] INFO     🔌 Connecting to RTSP camera: rtsp://***:***@*************:1935
[2025-09-06 11:32:34] WARNING  ⚠️ Failed to open RTSP stream (attempt 2)
[2025-09-06 11:32:34] INFO     🔄 Retry 3/3 in 4 seconds...
[2025-09-06 11:32:38] INFO     🔌 Connecting to RTSP camera: rtsp://***:***@*************:1935
[2025-09-06 11:32:38] WARNING  ⚠️ Failed to open RTSP stream (attempt 3)
[2025-09-06 11:32:38] ERROR    ❌ Failed to connect to RTSP camera after 3 attempts
[2025-09-06 11:32:38] ERROR    ❌ Failed to reconnect for fresh capture
[2025-09-06 11:32:38] ERROR    ❌ Failed to capture fresh image
[2025-09-06 11:32:38] ERROR    ❌ Capture failed: Failed to capture fresh image
[2025-09-06 11:32:45] INFO     📸 Manual capture initiated from web interface
[2025-09-06 11:32:45] INFO     🔄 Using fresh capture mode for manual trigger
[2025-09-06 11:32:45] INFO     🔄 Forcing fresh capture with camera reconnection...
[2025-09-06 11:32:45] INFO     🔌 Disconnected from RTSP camera
[2025-09-06 11:32:46] INFO     🔌 Connecting to RTSP camera: rtsp://***:***@*************:1935
[2025-09-06 11:32:46] WARNING  ⚠️ Failed to open RTSP stream (attempt 1)
[2025-09-06 11:32:46] INFO     🔄 Retry 2/3 in 2 seconds...
[2025-09-06 11:32:48] INFO     🔌 Connecting to RTSP camera: rtsp://***:***@*************:1935
[2025-09-06 11:32:48] WARNING  ⚠️ Failed to open RTSP stream (attempt 2)
[2025-09-06 11:32:48] INFO     🔄 Retry 3/3 in 4 seconds...
[2025-09-06 11:32:52] INFO     🔌 Connecting to RTSP camera: rtsp://***:***@*************:1935
[2025-09-06 11:32:52] WARNING  ⚠️ Failed to open RTSP stream (attempt 3)
[2025-09-06 11:32:52] ERROR    ❌ Failed to connect to RTSP camera after 3 attempts
[2025-09-06 11:32:52] ERROR    ❌ Failed to reconnect for fresh capture
[2025-09-06 11:32:52] ERROR    ❌ Failed to capture fresh image
[2025-09-06 11:32:52] ERROR    ❌ Capture failed: Failed to capture fresh image
[2025-09-06 11:33:06] INFO     📸 Manual capture initiated from web interface
[2025-09-06 11:33:06] INFO     🔄 Using fresh capture mode for manual trigger
[2025-09-06 11:33:06] INFO     🔄 Forcing fresh capture with camera reconnection...
[2025-09-06 11:33:06] INFO     🔌 Disconnected from RTSP camera
[2025-09-06 11:33:06] INFO     🔌 Connecting to RTSP camera: rtsp://***:***@*************:1935
[2025-09-06 11:33:10] INFO     ✅ Successfully connected to RTSP camera (retry 1, 2 successful reads)
[2025-09-06 11:33:10] INFO     📐 Frame size: (480, 640, 3)
[2025-09-06 11:33:10] INFO     ✅ Image captured and saved: captured_images/capture_20250906_113310_581338.jpg
[2025-09-06 11:33:10] INFO     Image captured successfully: captured_images/capture_20250906_113310_581338.jpg
[2025-09-06 11:33:10] INFO     📷 Fresh image captured: capture_20250906_113310_581338.jpg
[2025-09-06 11:33:10] INFO     🤖 Processing with AI (License Plate Detection)...
[2025-09-06 11:33:10] INFO     Processing image for License Plate Detection: captured_images/capture_20250906_113310_581338.jpg
[2025-09-06 11:33:12] INFO     API call successful: {'success': True, 'license_plates': [{'plate_number': '15 3870461', 'location': 'front of yellow truck', 'confidence': 'high', 'details': 'Partially obscured by decorations on the truck; likely an Indian license plate format.'}], 'plates_detected': 1, 'raw_response': '```\nLICENSE PLATES DETECTED: 1\n\nPlate 1:\n- Number: 15 3870461\n- Location: front of yellow truck\n- Confidence: high\n- Details: Partially obscured by decorations on the truck; likely an Indian license plate format.\n```', 'image_path': 'captured_images/capture_20250906_113310_581338.jpg'}
[2025-09-06 11:33:12] INFO     License plates detected in captured_images/capture_20250906_113310_581338.jpg: [{'plate_number': '15 3870461', 'location': 'front of yellow truck', 'confidence': 'high', 'details': 'Partially obscured by decorations on the truck; likely an Indian license plate format.'}]
[2025-09-06 11:33:12] INFO     🎯 Found 1 license plate(s)!
[2025-09-06 11:33:12] INFO     🏷️  Plate 1: 15 3870461 | front of yellow truck | Confidence: high
[2025-09-06 11:33:12] INFO     DETECTED: 15 3870461 | front of yellow truck | high | capture_20250906_113310_581338.jpg
[2025-09-06 11:37:19] INFO     🔑 Using API key from config.json (ignoring environment variable)
[2025-09-06 11:37:19] INFO     ✅ Configuration loaded from config.json
[2025-09-06 11:37:20] INFO     🎯 Operating in License Plate Detection mode
[2025-09-06 11:37:20] INFO     📹 Initializing real RTSP camera
[2025-09-06 11:37:20] INFO     🔑 Using API key from config.json: AIzaSyDQ...yfuk
[2025-09-06 11:37:20] INFO     ✅ Gemini AI client initialized successfully
[2025-09-06 11:37:20] INFO     ✅ AI Vision Monitor initialized for web interface
[2025-09-06 11:39:37] INFO     Received signal 2, shutting down gracefully...
[2025-09-06 11:39:37] INFO     🛑 Stopping License Plate Monitoring System...
[2025-09-06 11:39:37] INFO     🔌 Disconnected from RTSP camera
[2025-09-06 11:39:37] INFO     ✅ License Plate Monitoring System stopped
[2025-09-06 11:41:07] INFO     Received signal 2, shutting down gracefully...
[2025-09-06 11:41:07] INFO     🛑 Stopping License Plate Monitoring System...
[2025-09-06 11:41:07] INFO     🔌 Disconnected from RTSP camera
[2025-09-06 11:41:07] INFO     ✅ License Plate Monitoring System stopped
[2025-09-06 11:41:08] INFO     Received signal 2, shutting down gracefully...
[2025-09-06 11:41:08] INFO     🛑 Stopping License Plate Monitoring System...
[2025-09-06 11:41:08] INFO     🔌 Disconnected from RTSP camera
[2025-09-06 11:41:08] INFO     ✅ License Plate Monitoring System stopped
[2025-09-06 11:41:08] INFO     Received signal 2, shutting down gracefully...
[2025-09-06 11:41:08] INFO     🛑 Stopping License Plate Monitoring System...
[2025-09-06 11:41:08] INFO     🔌 Disconnected from RTSP camera
[2025-09-06 11:41:08] INFO     ✅ License Plate Monitoring System stopped
[2025-09-06 11:41:09] INFO     Received signal 2, shutting down gracefully...
[2025-09-06 11:41:09] INFO     🛑 Stopping License Plate Monitoring System...
[2025-09-06 11:41:09] INFO     🔌 Disconnected from RTSP camera
[2025-09-06 11:41:09] INFO     ✅ License Plate Monitoring System stopped
[2025-09-06 11:42:57] INFO     🔑 Using API key from config.json (ignoring environment variable)
[2025-09-06 11:42:57] INFO     ✅ Configuration loaded from config.json
[2025-09-06 11:42:58] INFO     🎯 Operating in License Plate Detection mode
[2025-09-06 11:42:58] INFO     📹 Initializing real RTSP camera
[2025-09-06 11:42:58] INFO     🔑 Using API key from config.json: AIzaSyDQ...yfuk
[2025-09-06 11:42:58] INFO     ✅ Gemini AI client initialized successfully
[2025-09-06 11:42:58] INFO     ✅ AI Vision Monitor initialized for web interface
[2025-09-06 11:43:12] INFO     📸 Manual capture initiated from web interface
[2025-09-06 11:43:12] INFO     🔄 Using fresh capture mode for manual trigger
[2025-09-06 11:43:12] INFO     🔄 Forcing fresh capture with camera reconnection...
[2025-09-06 11:43:12] INFO     🔌 Disconnected from RTSP camera
[2025-09-06 11:43:13] INFO     🔌 Connecting to RTSP camera: rtsp://***:***@*************:1935
[2025-09-06 11:43:15] INFO     ✅ Successfully connected to RTSP camera (retry 1, 2 successful reads)
[2025-09-06 11:43:15] INFO     📐 Frame size: (480, 640, 3)
[2025-09-06 11:43:15] INFO     ✅ Image captured and saved: captured_images/capture_20250906_114315_601526.jpg
[2025-09-06 11:43:15] INFO     Image captured successfully: captured_images/capture_20250906_114315_601526.jpg
[2025-09-06 11:43:15] INFO     📷 Fresh image captured: capture_20250906_114315_601526.jpg
[2025-09-06 11:43:15] INFO     🤖 Processing with AI (License Plate Detection)...
[2025-09-06 11:43:15] INFO     Processing image for License Plate Detection: captured_images/capture_20250906_114315_601526.jpg
[2025-09-06 11:43:18] INFO     API call successful: {'success': True, 'license_plates': [], 'plates_detected': 0, 'raw_response': 'LICENSE PLATES DETECTED: 0\nNo license plates visible in this image.', 'image_path': 'captured_images/capture_20250906_114315_601526.jpg'}
[2025-09-06 11:43:18] INFO     No license plates detected in captured_images/capture_20250906_114315_601526.jpg
[2025-09-06 11:43:18] INFO     ℹ️  No license plates detected
[2025-09-06 11:43:55] INFO     📸 Manual capture initiated from web interface
[2025-09-06 11:43:55] INFO     🔄 Using fresh capture mode for manual trigger
[2025-09-06 11:43:55] INFO     🔄 Forcing fresh capture with camera reconnection...
[2025-09-06 11:43:55] INFO     🔌 Disconnected from RTSP camera
[2025-09-06 11:43:55] INFO     🔌 Connecting to RTSP camera: rtsp://***:***@*************:1935
[2025-09-06 11:43:56] WARNING  ⚠️ Failed to open RTSP stream (attempt 1)
[2025-09-06 11:43:56] INFO     🔄 Retry 2/3 in 2 seconds...
[2025-09-06 11:43:58] INFO     🔌 Connecting to RTSP camera: rtsp://***:***@*************:1935
[2025-09-06 11:43:58] WARNING  ⚠️ Failed to open RTSP stream (attempt 2)
[2025-09-06 11:43:58] INFO     🔄 Retry 3/3 in 4 seconds...
[2025-09-06 11:44:02] INFO     🔌 Connecting to RTSP camera: rtsp://***:***@*************:1935
[2025-09-06 11:44:02] WARNING  ⚠️ Failed to open RTSP stream (attempt 3)
[2025-09-06 11:44:02] ERROR    ❌ Failed to connect to RTSP camera after 3 attempts
[2025-09-06 11:44:02] ERROR    ❌ Failed to reconnect for fresh capture
[2025-09-06 11:44:02] ERROR    ❌ Failed to capture fresh image
[2025-09-06 11:44:02] ERROR    ❌ Capture failed: Failed to capture fresh image
[2025-09-06 11:44:33] INFO     📸 Manual capture initiated from web interface
[2025-09-06 11:44:33] INFO     🔄 Using fresh capture mode for manual trigger
[2025-09-06 11:44:33] INFO     🔄 Forcing fresh capture with camera reconnection...
[2025-09-06 11:44:33] INFO     🔌 Disconnected from RTSP camera
[2025-09-06 11:44:34] INFO     🔌 Connecting to RTSP camera: rtsp://***:***@*************:1935
[2025-09-06 11:44:35] INFO     ✅ Successfully connected to RTSP camera (retry 1, 2 successful reads)
[2025-09-06 11:44:35] INFO     📐 Frame size: (480, 640, 3)
[2025-09-06 11:44:36] INFO     ✅ Image captured and saved: captured_images/capture_20250906_114436_425203.jpg
[2025-09-06 11:44:36] INFO     Image captured successfully: captured_images/capture_20250906_114436_425203.jpg
[2025-09-06 11:44:36] INFO     📷 Fresh image captured: capture_20250906_114436_425203.jpg
[2025-09-06 11:44:36] INFO     🤖 Processing with AI (License Plate Detection)...
[2025-09-06 11:44:36] INFO     Processing image for License Plate Detection: captured_images/capture_20250906_114436_425203.jpg
[2025-09-06 11:44:39] INFO     API call successful: {'success': True, 'license_plates': [{'plate_number': 'MH01AE8017', 'location': 'On the screen of a laptop, likely a Google search result', 'confidence': 'High', 'details': 'Indian license plate'}, {'plate_number': '*********', 'location': 'On the screen of a laptop, likely a Google search result', 'confidence': 'High', 'details': 'Indian license plate'}, {'plate_number': 'MB 06 F5977', 'location': 'On the screen of a laptop, likely a Google search result', 'confidence': 'High', 'details': 'Indian license plate'}, {'plate_number': 'TN 87 C5105', 'location': 'On the screen of a laptop, likely a Google search result', 'confidence': 'High', 'details': 'Indian license plate'}, {'plate_number': 'RJ1', 'location': 'On the screen of a laptop, likely a Google search result', 'confidence': 'Low', 'details': 'Only partially visible, likely an Indian license plate'}], 'plates_detected': 5, 'raw_response': '```\nLICENSE PLATES DETECTED: 5\n\nPlate 1:\n- Number: MH01AE8017\n- Location: On the screen of a laptop, likely a Google search result\n- Confidence: High\n- Details: Indian license plate\n\nPlate 2:\n- Number: *********\n- Location: On the screen of a laptop, likely a Google search result\n- Confidence: High\n- Details: Indian license plate\n\nPlate 3:\n- Number: MB 06 F5977\n- Location: On the screen of a laptop, likely a Google search result\n- Confidence: High\n- Details: Indian license plate\n\nPlate 4:\n- Number: TN 87 C5105\n- Location: On the screen of a laptop, likely a Google search result\n- Confidence: High\n- Details: Indian license plate\n\nPlate 5:\n- Number: RJ1\n- Location: On the screen of a laptop, likely a Google search result\n- Confidence: Low\n- Details: Only partially visible, likely an Indian license plate\n```', 'image_path': 'captured_images/capture_20250906_114436_425203.jpg'}
[2025-09-06 11:44:39] INFO     License plates detected in captured_images/capture_20250906_114436_425203.jpg: [{'plate_number': 'MH01AE8017', 'location': 'On the screen of a laptop, likely a Google search result', 'confidence': 'High', 'details': 'Indian license plate'}, {'plate_number': '*********', 'location': 'On the screen of a laptop, likely a Google search result', 'confidence': 'High', 'details': 'Indian license plate'}, {'plate_number': 'MB 06 F5977', 'location': 'On the screen of a laptop, likely a Google search result', 'confidence': 'High', 'details': 'Indian license plate'}, {'plate_number': 'TN 87 C5105', 'location': 'On the screen of a laptop, likely a Google search result', 'confidence': 'High', 'details': 'Indian license plate'}, {'plate_number': 'RJ1', 'location': 'On the screen of a laptop, likely a Google search result', 'confidence': 'Low', 'details': 'Only partially visible, likely an Indian license plate'}]
[2025-09-06 11:44:39] INFO     🎯 Found 5 license plate(s)!
[2025-09-06 11:44:39] INFO     🏷️  Plate 1: MH01AE8017 | On the screen of a laptop, likely a Google search result | Confidence: High
[2025-09-06 11:44:39] INFO     DETECTED: MH01AE8017 | On the screen of a laptop, likely a Google search result | High | capture_20250906_114436_425203.jpg
[2025-09-06 11:44:39] INFO     🏷️  Plate 2: ********* | On the screen of a laptop, likely a Google search result | Confidence: High
[2025-09-06 11:44:39] INFO     DETECTED: ********* | On the screen of a laptop, likely a Google search result | High | capture_20250906_114436_425203.jpg
[2025-09-06 11:44:39] INFO     🏷️  Plate 3: MB 06 F5977 | On the screen of a laptop, likely a Google search result | Confidence: High
[2025-09-06 11:44:39] INFO     DETECTED: MB 06 F5977 | On the screen of a laptop, likely a Google search result | High | capture_20250906_114436_425203.jpg
[2025-09-06 11:44:39] INFO     🏷️  Plate 4: TN 87 C5105 | On the screen of a laptop, likely a Google search result | Confidence: High
[2025-09-06 11:44:39] INFO     DETECTED: TN 87 C5105 | On the screen of a laptop, likely a Google search result | High | capture_20250906_114436_425203.jpg
[2025-09-06 11:44:39] INFO     🏷️  Plate 5: RJ1 | On the screen of a laptop, likely a Google search result | Confidence: Low
[2025-09-06 11:44:39] INFO     DETECTED: RJ1 | On the screen of a laptop, likely a Google search result | Low | capture_20250906_114436_425203.jpg
[2025-09-06 11:51:17] INFO     📸 Manual capture initiated from web interface
[2025-09-06 11:51:17] INFO     🔄 Using fresh capture mode for manual trigger
[2025-09-06 11:51:17] INFO     🔄 Forcing fresh capture with camera reconnection...
[2025-09-06 11:51:17] INFO     🔌 Disconnected from RTSP camera
[2025-09-06 11:51:17] INFO     🔌 Connecting to RTSP camera: rtsp://***:***@*************:1935
[2025-09-06 11:51:20] INFO     ✅ Successfully connected to RTSP camera (retry 1, 2 successful reads)
[2025-09-06 11:51:20] INFO     📐 Frame size: (480, 640, 3)
[2025-09-06 11:51:20] INFO     ✅ Image captured and saved: captured_images/capture_20250906_115120_846301.jpg
[2025-09-06 11:51:20] INFO     Image captured successfully: captured_images/capture_20250906_115120_846301.jpg
[2025-09-06 11:51:20] INFO     📷 Fresh image captured: capture_20250906_115120_846301.jpg
[2025-09-06 11:51:20] INFO     🤖 Processing with AI (License Plate Detection)...
[2025-09-06 11:51:20] INFO     Processing image for License Plate Detection: captured_images/capture_20250906_115120_846301.jpg
[2025-09-06 11:51:23] INFO     API call successful: {'success': True, 'license_plates': [{'plate_number': 'WB06F5977', 'location': 'Image search result', 'confidence': 'high', 'details': 'India'}, {'plate_number': '22BH6517A', 'location': 'Image search result', 'confidence': 'high', 'details': 'India'}, {'plate_number': 'RJ14CY0002', 'location': 'Image search result', 'confidence': 'high', 'details': 'India'}], 'plates_detected': 3, 'raw_response': '```\nLICENSE PLATES DETECTED: 3\n\nPlate 1:\n- Number: WB06F5977\n- Location: Image search result\n- Confidence: high\n- Details: India\n\nPlate 2:\n- Number: 22BH6517A\n- Location: Image search result\n- Confidence: high\n- Details: India\n\nPlate 3:\n- Number: RJ14CY0002\n- Location: Image search result\n- Confidence: high\n- Details: India\n```', 'image_path': 'captured_images/capture_20250906_115120_846301.jpg'}
[2025-09-06 11:51:23] INFO     License plates detected in captured_images/capture_20250906_115120_846301.jpg: [{'plate_number': 'WB06F5977', 'location': 'Image search result', 'confidence': 'high', 'details': 'India'}, {'plate_number': '22BH6517A', 'location': 'Image search result', 'confidence': 'high', 'details': 'India'}, {'plate_number': 'RJ14CY0002', 'location': 'Image search result', 'confidence': 'high', 'details': 'India'}]
[2025-09-06 11:51:23] INFO     🎯 Found 3 license plate(s)!
[2025-09-06 11:51:23] INFO     🏷️  Plate 1: WB06F5977 | Image search result | Confidence: high
[2025-09-06 11:51:23] INFO     DETECTED: WB06F5977 | Image search result | high | capture_20250906_115120_846301.jpg
[2025-09-06 11:51:23] INFO     🏷️  Plate 2: 22BH6517A | Image search result | Confidence: high
[2025-09-06 11:51:23] INFO     DETECTED: 22BH6517A | Image search result | high | capture_20250906_115120_846301.jpg
[2025-09-06 11:51:23] INFO     🏷️  Plate 3: RJ14CY0002 | Image search result | Confidence: high
[2025-09-06 11:51:23] INFO     DETECTED: RJ14CY0002 | Image search result | high | capture_20250906_115120_846301.jpg
[2025-09-06 11:51:37] INFO     🔄 Mode changed to: object_detection
[2025-09-06 11:51:37] INFO     🔄 Detection mode changed to: object_detection
