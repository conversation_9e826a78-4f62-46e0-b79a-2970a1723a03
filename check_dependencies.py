#!/usr/bin/env python3
"""
Check and validate dependencies for the license plate recognition system
"""

import sys
import subprocess
import importlib.util

def check_package(package_name, import_name=None):
    """Check if a package is installed and get its version"""
    if import_name is None:
        import_name = package_name
    
    try:
        # Try to import the package
        spec = importlib.util.find_spec(import_name)
        if spec is None:
            return False, "Not installed"
        
        # Try to get version
        try:
            module = importlib.import_module(import_name)
            version = getattr(module, '__version__', 'Unknown version')
            return True, version
        except Exception:
            return True, "Installed (version unknown)"
            
    except Exception as e:
        return False, str(e)

def main():
    """Check all dependencies"""
    print("🔍 Checking Dependencies")
    print("=" * 40)
    
    # List of required packages
    packages = [
        ('google-genai', 'google.genai'),
        ('opencv-python', 'cv2'),
        ('pillow', 'PIL'),
        ('flask', 'flask'),
        ('flask-cors', 'flask_cors'),
        ('python-dotenv', 'dotenv'),
    ]
    
    all_good = True
    
    for package_name, import_name in packages:
        installed, version = check_package(package_name, import_name)
        status = "✅" if installed else "❌"
        print(f"{status} {package_name}: {version}")
        
        if not installed:
            all_good = False
    
    print(f"\n🐍 Python Version: {sys.version}")
    
    if not all_good:
        print(f"\n❌ Some dependencies are missing!")
        print("💡 To install missing packages, run:")
        print("   pip install google-genai opencv-python pillow flask flask-cors python-dotenv")
    else:
        print(f"\n✅ All dependencies are installed!")
    
    # Test google-genai specifically
    print(f"\n🧪 Testing google-genai import...")
    try:
        import warnings
        # Suppress pydantic warnings from Google GenAI library
        warnings.filterwarnings("ignore", message="Field name .* shadows an attribute in parent .*", category=UserWarning)

        from google import genai
        from google.genai import types
        print("✅ google-genai imports successfully")
        
        # Test client creation (without API key)
        try:
            # This should work even without API key
            print("🔧 Testing client creation...")
            client = genai.Client(api_key="test")
            print("✅ Client creation works")
        except Exception as e:
            print(f"⚠️ Client creation issue: {e}")
            
    except ImportError as e:
        print(f"❌ google-genai import failed: {e}")
        print("💡 Try: pip install --upgrade google-genai")
    
    # Check for conflicting packages
    print(f"\n🔍 Checking for conflicting packages...")
    conflicting_packages = [
        'google-generativeai',  # Old package that might conflict
        'generativeai'
    ]
    
    for package in conflicting_packages:
        installed, version = check_package(package)
        if installed:
            print(f"⚠️ Conflicting package found: {package} ({version})")
            print(f"   Consider uninstalling: pip uninstall {package}")
        else:
            print(f"✅ No conflicting package: {package}")

if __name__ == "__main__":
    main()
